#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集统计分析脚本
分析清理后的数据集的各种统计信息
"""

import os
import re
import json
import logging
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('dataset_stats.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def extract_category_from_filename(filename):
    """从文件名提取分类"""
    # 常见分类模式
    categories = [
        '都市激情', '家庭乱伦', '人妻交换', '校园春色', 
        '制服丝袜', '同性恋情', '其他'
    ]
    
    for category in categories:
        if category in filename:
            return category
    
    return '未分类'

def analyze_content_length(content):
    """分析内容长度"""
    # 去除空白字符后的长度
    clean_content = re.sub(r'\s+', '', content)
    return len(clean_content)

def extract_keywords(content):
    """提取关键词"""
    # 简单的关键词提取
    keywords = []
    
    # 常见NSFW关键词（用于分类统计）
    nsfw_keywords = [
        '调教', '迷奸', '丝袜', '激情', '乱伦', '人妻', 
        '校园', '制服', '同性', '按摩', '地铁', '老婆',
        '表姐', '表妹', '妈妈', '姐姐', '妹妹', '女友'
    ]
    
    content_lower = content.lower()
    for keyword in nsfw_keywords:
        if keyword in content:
            keywords.append(keyword)
    
    return keywords

def analyze_dataset():
    """分析数据集"""
    logger = setup_logging()
    dataset_dir = Path("dataset")
    
    if not dataset_dir.exists():
        logger.error(f"数据集目录不存在: {dataset_dir}")
        return
    
    logger.info("开始分析数据集...")
    
    # 统计数据结构
    stats = {
        'total_files': 0,
        'total_characters': 0,
        'total_words': 0,
        'categories': defaultdict(int),
        'file_sizes': [],
        'content_lengths': [],
        'keywords': Counter(),
        'filename_patterns': Counter(),
        'encoding_issues': 0,
        'empty_files': 0
    }
    
    txt_files = list(dataset_dir.glob("*.txt"))
    stats['total_files'] = len(txt_files)
    
    logger.info(f"找到 {len(txt_files)} 个txt文件")
    
    for i, file_path in enumerate(txt_files):
        if i % 50 == 0:
            logger.info(f"分析进度: {i}/{len(txt_files)}")
        
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 文件大小
            file_size = file_path.stat().st_size
            stats['file_sizes'].append(file_size)
            
            # 内容长度
            content_length = analyze_content_length(content)
            stats['content_lengths'].append(content_length)
            stats['total_characters'] += content_length
            
            # 词数统计（简单按空格分割）
            word_count = len(content.split())
            stats['total_words'] += word_count
            
            # 分类统计
            category = extract_category_from_filename(file_path.name)
            stats['categories'][category] += 1
            
            # 关键词统计
            keywords = extract_keywords(content)
            for keyword in keywords:
                stats['keywords'][keyword] += 1
            
            # 文件名模式统计
            if file_path.name.startswith('NSFW_'):
                stats['filename_patterns']['NSFW_前缀'] += 1
            if '标题_' in file_path.name:
                stats['filename_patterns']['包含标题'] += 1
            if '来源' in file_path.name:
                stats['filename_patterns']['包含来源'] += 1
            
            # 检查空文件
            if content_length < 50:
                stats['empty_files'] += 1
                logger.warning(f"内容过少的文件: {file_path.name} ({content_length} 字符)")
        
        except Exception as e:
            logger.error(f"分析文件失败 {file_path}: {e}")
            stats['encoding_issues'] += 1
    
    # 计算统计指标
    if stats['content_lengths']:
        stats['avg_content_length'] = sum(stats['content_lengths']) / len(stats['content_lengths'])
        stats['min_content_length'] = min(stats['content_lengths'])
        stats['max_content_length'] = max(stats['content_lengths'])
    
    if stats['file_sizes']:
        stats['avg_file_size'] = sum(stats['file_sizes']) / len(stats['file_sizes'])
        stats['total_size_mb'] = sum(stats['file_sizes']) / (1024 * 1024)
    
    # 生成报告
    generate_report(stats, logger)
    
    # 保存详细统计数据
    save_detailed_stats(stats)
    
    logger.info("数据集分析完成")

def generate_report(stats, logger):
    """生成分析报告"""
    logger.info("=" * 80)
    logger.info("数据集统计分析报告")
    logger.info("=" * 80)
    
    # 基本统计
    logger.info(f"文件总数: {stats['total_files']}")
    logger.info(f"总字符数: {stats['total_characters']:,}")
    logger.info(f"总词数: {stats['total_words']:,}")
    logger.info(f"总大小: {stats.get('total_size_mb', 0):.2f} MB")
    logger.info(f"编码问题文件: {stats['encoding_issues']}")
    logger.info(f"内容过少文件: {stats['empty_files']}")
    
    # 内容长度统计
    if 'avg_content_length' in stats:
        logger.info(f"平均内容长度: {stats['avg_content_length']:.0f} 字符")
        logger.info(f"最短内容长度: {stats['min_content_length']} 字符")
        logger.info(f"最长内容长度: {stats['max_content_length']:,} 字符")
    
    # 分类统计
    logger.info("\n分类分布:")
    for category, count in sorted(stats['categories'].items(), key=lambda x: x[1], reverse=True):
        percentage = (count / stats['total_files']) * 100
        logger.info(f"  {category}: {count} 个文件 ({percentage:.1f}%)")
    
    # 热门关键词
    logger.info("\n热门关键词 (前10):")
    for keyword, count in stats['keywords'].most_common(10):
        percentage = (count / stats['total_files']) * 100
        logger.info(f"  {keyword}: {count} 次 ({percentage:.1f}%)")
    
    # 文件名模式
    logger.info("\n文件名模式:")
    for pattern, count in stats['filename_patterns'].items():
        percentage = (count / stats['total_files']) * 100
        logger.info(f"  {pattern}: {count} 个文件 ({percentage:.1f}%)")
    
    logger.info("=" * 80)

def save_detailed_stats(stats):
    """保存详细统计数据到JSON文件"""
    # 转换Counter对象为普通字典以便JSON序列化
    json_stats = {
        'timestamp': datetime.now().isoformat(),
        'basic_stats': {
            'total_files': stats['total_files'],
            'total_characters': stats['total_characters'],
            'total_words': stats['total_words'],
            'total_size_mb': stats.get('total_size_mb', 0),
            'avg_content_length': stats.get('avg_content_length', 0),
            'min_content_length': stats.get('min_content_length', 0),
            'max_content_length': stats.get('max_content_length', 0),
            'avg_file_size': stats.get('avg_file_size', 0),
            'encoding_issues': stats['encoding_issues'],
            'empty_files': stats['empty_files']
        },
        'categories': dict(stats['categories']),
        'top_keywords': dict(stats['keywords'].most_common(20)),
        'filename_patterns': dict(stats['filename_patterns']),
        'content_length_distribution': {
            'under_1k': len([x for x in stats['content_lengths'] if x < 1000]),
            '1k_5k': len([x for x in stats['content_lengths'] if 1000 <= x < 5000]),
            '5k_10k': len([x for x in stats['content_lengths'] if 5000 <= x < 10000]),
            '10k_50k': len([x for x in stats['content_lengths'] if 10000 <= x < 50000]),
            'over_50k': len([x for x in stats['content_lengths'] if x >= 50000])
        }
    }
    
    stats_file = Path("dataset_statistics.json")
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(json_stats, f, ensure_ascii=False, indent=2)
    
    print(f"详细统计数据已保存到: {stats_file}")

if __name__ == "__main__":
    analyze_dataset()
