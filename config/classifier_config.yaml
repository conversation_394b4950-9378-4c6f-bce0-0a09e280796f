# 分类器配置文件

classifier:
  # 基础配置
  model_type: "svm"               # 模型类型: svm, naive_bayes, random_forest
  test_size: 0.2                  # 测试集比例
  random_state: 42               # 随机种子
  
  # 文本预处理
  preprocessing:
    min_content_length: 100       # 最小内容长度
    max_content_length: 100000    # 最大内容长度
    remove_stopwords: true        # 是否移除停用词
    lowercase: true               # 是否转换为小写
    remove_punctuation: true      # 是否移除标点符号
  
  # TF-IDF配置
  tfidf:
    max_features: 10000           # 最大特征数
    min_df: 2                     # 最小文档频率
    max_df: 0.95                  # 最大文档频率
    ngram_range: [1, 2]           # N-gram范围
    use_idf: true                 # 是否使用IDF
    sublinear_tf: true            # 是否使用次线性TF缩放
  
  # SVM配置
  svm:
    C: 1.0                        # 正则化参数
    kernel: "linear"              # 核函数类型
    gamma: "scale"                # 核函数系数
    class_weight: "balanced"      # 类别权重
  
  # 朴素贝叶斯配置
  naive_bayes:
    alpha: 1.0                    # 平滑参数
  
  # 随机森林配置
  random_forest:
    n_estimators: 100             # 树的数量
    max_depth: 10                 # 最大深度
    min_samples_split: 2          # 最小分割样本数
    min_samples_leaf: 1           # 最小叶子样本数
    class_weight: "balanced"      # 类别权重

# 分类体系
categories:
  nsfw:
    - "都市激情"
    - "家庭乱伦"
    - "人妻交换"
    - "校园春色"
    - "制服丝袜"
    - "同性恋情"
    - "其他成人内容"
  
  safe:
    - "教育指导"
    - "健康养生"
    - "友情故事"
    - "科技文章"
    - "职场励志"
    - "旅行游记"
    - "美食文化"
    - "环保教育"

# 去重配置
deduplication:
  similarity_threshold: 0.9       # 相似度阈值
  hash_algorithm: "md5"           # 哈希算法
  
  # 内容标准化
  normalize:
    remove_whitespace: true       # 移除空白字符
    remove_punctuation: true      # 移除标点符号
    remove_numbers: false         # 移除数字
  
  # 相似度计算
  similarity:
    method: "cosine"              # 相似度计算方法
    min_length: 50                # 最小比较长度
    max_length: 3000              # 最大比较长度

# 质量评估
quality:
  # 评分权重
  weights:
    length: 0.2                   # 长度权重
    uniqueness: 0.3               # 唯一性权重
    readability: 0.2              # 可读性权重
    relevance: 0.3                # 相关性权重
  
  # 长度评分
  length_scoring:
    min_good_length: 1000         # 良好最小长度
    optimal_length: 5000          # 最佳长度
    max_good_length: 50000        # 良好最大长度
  
  # 可读性评分
  readability:
    min_sentence_length: 5        # 最小句子长度
    max_sentence_length: 100      # 最大句子长度
    punctuation_ratio: 0.1        # 标点符号比例

# 输出配置
output:
  model_dir: "data/models"        # 模型保存目录
  report_dir: "data/reports"      # 报告保存目录
  
  # 模型文件名
  classifier_filename: "nsfw_classifier.pkl"
  vectorizer_filename: "tfidf_vectorizer.pkl"
  
  # 报告格式
  report_format: ["json", "html", "txt"]
  
  # 是否保存详细日志
  save_detailed_logs: true
