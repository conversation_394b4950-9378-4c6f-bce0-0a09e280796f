# 爬虫配置文件

scraper:
  # 基础配置
  max_workers: 5                    # 最大并发线程数
  delay_range: [1, 3]              # 请求延迟范围(秒)
  max_retries: 3                   # 最大重试次数
  timeout: 30                      # 请求超时时间(秒)
  
  # 用户代理列表
  user_agents:
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0"
    - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0"
  
  # 请求头配置
  headers:
    Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
    Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8"
    Accept-Encoding: "gzip, deflate, br"
    Connection: "keep-alive"
    Upgrade-Insecure-Requests: "1"
  
  # 代理配置 (可选)
  proxy:
    enabled: false
    http: ""
    https: ""
  
  # 站点特定配置
  sites:
    yazhouse8:
      base_url: "https://yazhouse8.com"
      category_pages:
        - "/category/都市激情"
        - "/category/家庭乱伦"
        - "/category/人妻交换"
        - "/category/校园春色"
        - "/category/制服丝袜"
      max_pages: 100
      content_selector: ".content"
      title_selector: ".title"
    
    xchina:
      base_url: "https://xchina.co"
      category_pages:
        - "/forum-2-1.html"
        - "/forum-3-1.html"
      max_pages: 50
      content_selector: ".t_msgfont"
      title_selector: ".s.xst"

# 内容过滤配置
content_filter:
  min_length: 100                  # 最小内容长度
  max_length: 1000000             # 最大内容长度
  
  # 关键词过滤
  required_keywords:              # 必须包含的关键词
    - "激情"
    - "诱惑"
    - "调教"
  
  excluded_keywords:              # 排除的关键词
    - "广告"
    - "推广"
    - "联系方式"
  
  # 质量评分阈值
  quality_threshold: 6.0          # 最低质量评分

# 输出配置
output:
  base_dir: "dataset"             # 输出基础目录
  backup_dir: "dataset_backup"    # 备份目录
  
  # 文件命名格式
  filename_format: "NSFW_标题_{title}_分类_{category}.txt"
  
  # 编码设置
  encoding: "utf-8"
  
  # 是否保存元数据
  save_metadata: true
