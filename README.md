# NSFW内容检测数据集项目

[![Python Version](https://img.shields.io/badge/python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Code Style](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

这是一个专业的NSFW（Not Safe For Work）内容数据集收集、处理和分析项目，主要用于训练内容审核和分类模型。

## 🏗️ 项目结构

```
NSFW/
├── README.md                           # 项目主文档
├── LICENSE                             # 许可证文件
├── .gitignore                         # Git忽略文件
├── src/                               # 源代码目录
│   ├── scrapers/                      # 爬虫模块
│   │   ├── __init__.py
│   │   ├── novel_scraper.py           # 基础小说爬虫
│   │   ├── advanced_scraper.py        # 高级多线程爬虫
│   │   ├── yazhouse8_ultimate_scraper.py  # 专用爬虫
│   │   └── xchina_scraper_standalone.py  # 独立爬虫
│   ├── data_processing/               # 数据处理模块
│   │   ├── __init__.py
│   │   ├── data_cleaner.py           # 数据清洗工具
│   │   ├── dataset_deduplicator.py   # 数据去重工具
│   │   └── finalize_dataset.py       # 数据集最终整理
│   ├── analysis/                      # 数据分析模块
│   │   ├── __init__.py
│   │   ├── dataset_stats.py          # 数据集统计分析
│   │   └── content_classifier.py     # 内容分类器
│   └── utils/                         # 工具模块
│       ├── __init__.py
│       └── dataset_generator.py      # 数据集生成器
├── config/                            # 配置文件目录
│   ├── requirements.txt               # Python依赖
│   ├── scraper_config.yaml           # 爬虫配置
│   └── classifier_config.yaml        # 分类器配置
├── data/                              # 数据文件目录
│   ├── models/                        # 训练好的模型
│   ├── metadata/                      # 元数据文件
│   └── statistics/                    # 统计数据
├── dataset/                           # 主数据集目录
├── dataset_backup/                    # 数据集备份
├── logs/                              # 日志文件目录
├── scripts/                           # 脚本文件目录
│   └── remove_duplicates.sh          # 去重脚本
└── docs/                              # 文档目录
    ├── API.md                         # API文档
    ├── DEVELOPMENT.md                 # 开发指南
    └── DEPLOYMENT.md                  # 部署指南
```

## ✨ 功能特性

### 🕷️ 智能爬虫系统
- **多站点支持**: 支持多个NSFW内容站点
- **并发爬取**: 多线程并发，提高爬取效率
- **智能反爬**: 自动处理反爬机制和网络异常
- **断点续传**: 支持中断后继续爬取
- **内容过滤**: 实时过滤低质量内容

### 🔧 数据处理引擎
- **智能去重**: 基于内容哈希和相似度的去重算法
- **内容清洗**: 自动清理格式、编码和无效内容
- **质量评估**: 多维度内容质量评分
- **分类标注**: 自动内容分类和标签提取
- **格式标准化**: 统一的数据格式和结构

### 📊 数据分析工具
- **统计分析**: 全面的数据集统计信息
- **可视化报告**: 图表化的数据分析报告
- **质量监控**: 实时数据质量监控
- **趋势分析**: 内容趋势和分布分析

### 🎯 内容分类系统
- **机器学习分类**: 基于TF-IDF和SVM的分类器
- **多标签支持**: 支持多标签分类
- **自定义分类**: 可配置的分类体系
- **准确率优化**: 持续优化分类准确率

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 8GB+ RAM (推荐)
- 10GB+ 可用磁盘空间

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd NSFW
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r config/requirements.txt
```

4. **初始化项目**
```bash
python -m src.utils.dataset_generator --init
```

### 基本使用

#### 1. 数据爬取
```bash
# 基础爬虫
python -m src.scrapers.novel_scraper

# 高级爬虫（推荐）
python -m src.scrapers.advanced_scraper --threads 5 --delay 2

# 特定站点爬虫
python -m src.scrapers.yazhouse8_ultimate_scraper
```

#### 2. 数据处理
```bash
# 数据清洗
python -m src.data_processing.data_cleaner

# 数据去重
python -m src.data_processing.dataset_deduplicator

# 最终整理
python -m src.data_processing.finalize_dataset
```

#### 3. 数据分析
```bash
# 统计分析
python -m src.analysis.dataset_stats

# 内容分类
python -m src.analysis.content_classifier --train
```

## 📈 数据集信息

### 当前统计 (最新更新: 2025-06-21)
- **文件总数**: 262个
- **总字符数**: 9,737,618字符
- **总大小**: 27.88 MB
- **去重率**: 81.4%
- **平均内容长度**: 37,166字符

### 分类分布
| 分类 | 文件数 | 占比 |
|------|--------|------|
| 未分类 | 105 | 40.1% |
| 都市激情 | 74 | 28.2% |
| 人妻交换 | 38 | 14.5% |
| 家庭乱伦 | 21 | 8.0% |
| 校园春色 | 11 | 4.2% |
| 其他 | 9 | 3.4% |
| 制服丝袜 | 3 | 1.1% |
| 同性恋情 | 1 | 0.4% |

### 热门关键词
1. 老婆 (59.5%)
2. 激情 (57.3%)
3. 妈妈 (56.9%)
4. 姐姐 (45.8%)
5. 丝袜 (42.7%)

## ⚙️ 配置说明

### 爬虫配置 (`config/scraper_config.yaml`)
```yaml
scraper:
  max_workers: 5          # 最大并发数
  delay_range: [1, 3]     # 请求延迟范围(秒)
  max_retries: 3          # 最大重试次数
  timeout: 30             # 请求超时时间
  user_agents:            # 用户代理列表
    - "Mozilla/5.0 ..."
```

### 分类器配置 (`config/classifier_config.yaml`)
```yaml
classifier:
  min_content_length: 100      # 最小内容长度
  similarity_threshold: 0.9    # 相似度阈值
  categories:                  # 分类体系
    - "都市激情"
    - "家庭乱伦"
    # ...
```

## 🔧 开发指南

### 代码规范
- 使用 Black 进行代码格式化
- 遵循 PEP 8 编码规范
- 添加类型注解和文档字符串
- 编写单元测试

### 测试
```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_scrapers.py

# 生成覆盖率报告
python -m pytest --cov=src tests/
```

### 贡献流程
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📝 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## ⚠️ 免责声明

本项目仅用于学术研究和技术开发目的。使用者需要：

1. 遵守当地法律法规
2. 尊重网站使用条款
3. 合理控制爬取频率
4. 仅将数据用于合法研究

使用本项目产生的任何法律责任由使用者自行承担。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 [Issue](https://github.com/your-repo/issues)
- 发送邮件至：<EMAIL>

---

**注意**: 本项目专注于内容安全技术研究，请确保合规使用。
