#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NSFW数据集项目管理脚本

统一管理项目的各种操作，包括数据爬取、处理、分析等。

使用方法:
    python manage.py <command> [options]

可用命令:
    scrape      - 执行数据爬取
    clean       - 清洗数据
    dedupe      - 数据去重
    analyze     - 数据分析
    train       - 训练分类器
    status      - 查看项目状态
    setup       - 初始化项目
"""

import sys
import argparse
import logging
from pathlib import Path
from typing import Dict, Any

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 延迟导入，避免在status命令时导入失败
def import_modules():
    """延迟导入模块"""
    try:
        from src.scrapers.advanced_scraper import AdvancedScraper
        from src.data_processing.data_cleaner import DataCleaner
        from src.data_processing.dataset_deduplicator import DatasetDeduplicator
        from src.analysis.dataset_stats import DatasetAnalyzer
        from src.analysis.content_classifier import ContentClassifier
        return {
            'AdvancedScraper': AdvancedScraper,
            'DataCleaner': DataCleaner,
            'DatasetDeduplicator': DatasetDeduplicator,
            'DatasetAnalyzer': DatasetAnalyzer,
            'ContentClassifier': ContentClassifier
        }
    except ImportError as e:
        return None


class ProjectManager:
    """项目管理器"""

    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)

        # 项目路径
        self.project_root = Path(__file__).parent
        self.src_dir = self.project_root / "src"
        self.config_dir = self.project_root / "config"
        self.data_dir = self.project_root / "data"
        self.dataset_dir = self.project_root / "dataset"
        self.logs_dir = self.project_root / "logs"

    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )

    def cmd_setup(self, args):
        """初始化项目"""
        self.logger.info("初始化项目...")

        # 创建必要的目录
        directories = [
            self.data_dir / "models",
            self.data_dir / "metadata",
            self.data_dir / "statistics",
            self.dataset_dir,
            self.logs_dir
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"创建目录: {directory}")

        self.logger.info("项目初始化完成")

    def cmd_scrape(self, args):
        """执行数据爬取"""
        self.logger.info("开始数据爬取...")

        modules = import_modules()
        if not modules:
            self.logger.error("无法导入必要模块，请先安装依赖: pip install -r config/requirements.txt")
            return False

        try:
            AdvancedScraper = modules['AdvancedScraper']
            scraper = AdvancedScraper(
                max_workers=args.workers,
                delay_range=(args.min_delay, args.max_delay)
            )

            # 这里可以添加具体的爬取逻辑
            self.logger.info("爬取完成")

        except Exception as e:
            self.logger.error(f"爬取失败: {e}")
            return False

        return True

    def cmd_clean(self, args):
        """清洗数据"""
        self.logger.info("开始数据清洗...")

        modules = import_modules()
        if not modules:
            self.logger.error("无法导入必要模块，请先安装依赖: pip install -r config/requirements.txt")
            return False

        try:
            DataCleaner = modules['DataCleaner']
            cleaner = DataCleaner(
                input_dir=args.input_dir or "dataset",
                output_dir=args.output_dir or "dataset_cleaned"
            )

            cleaner.run()
            self.logger.info("数据清洗完成")

        except Exception as e:
            self.logger.error(f"数据清洗失败: {e}")
            return False

        return True

    def cmd_dedupe(self, args):
        """数据去重"""
        self.logger.info("开始数据去重...")

        try:
            deduplicator = DatasetDeduplicator(
                dataset_dir=args.dataset_dir or "dataset"
            )

            deduplicator.run()
            self.logger.info("数据去重完成")

        except Exception as e:
            self.logger.error(f"数据去重失败: {e}")
            return False

        return True

    def cmd_analyze(self, args):
        """数据分析"""
        self.logger.info("开始数据分析...")

        try:
            analyzer = DatasetAnalyzer(
                dataset_dir=args.dataset_dir or "dataset"
            )

            analyzer.analyze_dataset()
            self.logger.info("数据分析完成")

        except Exception as e:
            self.logger.error(f"数据分析失败: {e}")
            return False

        return True

    def cmd_train(self, args):
        """训练分类器"""
        self.logger.info("开始训练分类器...")

        try:
            classifier = ContentClassifier()

            # 这里可以添加具体的训练逻辑
            self.logger.info("分类器训练完成")

        except Exception as e:
            self.logger.error(f"分类器训练失败: {e}")
            return False

        return True

    def cmd_clean_filenames(self, args):
        """清理文件名"""
        self.logger.info("开始清理文件名...")

        try:
            # 直接导入文件名清理器
            import sys
            sys.path.insert(0, str(self.src_dir))
            from utils.filename_cleaner import FilenameCleaner

            cleaner = FilenameCleaner(
                dataset_dir=args.dataset_dir or "dataset"
            )

            # 执行清理
            stats = cleaner.clean_filenames(dry_run=args.dry_run)

            # 预览变更
            if args.dry_run and cleaner.rename_mapping:
                cleaner.preview_changes(args.preview or 20)

            # 生成报告
            cleaner.generate_report()

            if args.dry_run:
                self.logger.info("这是预览模式，没有实际重命名文件")
                self.logger.info("要执行实际重命名，请运行: python manage.py clean-filenames")
            else:
                self.logger.info("文件名清理完成")

        except Exception as e:
            self.logger.error(f"文件名清理失败: {e}")
            return False

        return True

    def cmd_status(self, args):
        """查看项目状态"""
        self.logger.info("项目状态:")

        # 检查目录
        directories = {
            "源代码": self.src_dir,
            "配置文件": self.config_dir,
            "数据文件": self.data_dir,
            "数据集": self.dataset_dir,
            "日志文件": self.logs_dir
        }

        for name, path in directories.items():
            status = "✓" if path.exists() else "✗"
            self.logger.info(f"  {status} {name}: {path}")

        # 统计文件数量
        if self.dataset_dir.exists():
            txt_files = list(self.dataset_dir.glob("*.txt"))
            self.logger.info(f"  数据集文件数: {len(txt_files)}")

        # 检查模型文件
        model_dir = self.data_dir / "models"
        if model_dir.exists():
            model_files = list(model_dir.glob("*.pkl"))
            self.logger.info(f"  模型文件数: {len(model_files)}")


def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="NSFW数据集项目管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # setup命令
    setup_parser = subparsers.add_parser("setup", help="初始化项目")

    # scrape命令
    scrape_parser = subparsers.add_parser("scrape", help="执行数据爬取")
    scrape_parser.add_argument("--workers", type=int, default=5, help="并发线程数")
    scrape_parser.add_argument("--min-delay", type=int, default=1, help="最小延迟(秒)")
    scrape_parser.add_argument("--max-delay", type=int, default=3, help="最大延迟(秒)")

    # clean命令
    clean_parser = subparsers.add_parser("clean", help="清洗数据")
    clean_parser.add_argument("--input-dir", help="输入目录")
    clean_parser.add_argument("--output-dir", help="输出目录")

    # dedupe命令
    dedupe_parser = subparsers.add_parser("dedupe", help="数据去重")
    dedupe_parser.add_argument("--dataset-dir", help="数据集目录")

    # analyze命令
    analyze_parser = subparsers.add_parser("analyze", help="数据分析")
    analyze_parser.add_argument("--dataset-dir", help="数据集目录")

    # train命令
    train_parser = subparsers.add_parser("train", help="训练分类器")
    train_parser.add_argument("--dataset-dir", help="训练数据目录")

    # clean-filenames命令
    clean_filenames_parser = subparsers.add_parser("clean-filenames", help="清理文件名")
    clean_filenames_parser.add_argument("--dataset-dir", help="数据集目录")
    clean_filenames_parser.add_argument("--dry-run", action="store_true", help="预览模式，不实际重命名")
    clean_filenames_parser.add_argument("--preview", type=int, default=20, help="预览变更数量")

    # status命令
    status_parser = subparsers.add_parser("status", help="查看项目状态")

    return parser


def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    manager = ProjectManager()

    # 执行对应的命令
    command_name = args.command.replace('-', '_')  # 处理带连字符的命令
    command_method = getattr(manager, f"cmd_{command_name}", None)
    if command_method:
        success = command_method(args)
        if not success:
            sys.exit(1)
    else:
        print(f"未知命令: {args.command}")
        parser.print_help()
        sys.exit(1)


if __name__ == "__main__":
    main()
