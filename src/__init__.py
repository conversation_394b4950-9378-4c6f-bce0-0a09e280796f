"""
NSFW内容检测数据集项目

这是一个专业的NSFW内容数据集收集、处理和分析项目。
主要用于训练内容审核和分类模型。

模块结构:
- scrapers: 网络爬虫模块
- data_processing: 数据处理模块  
- analysis: 数据分析模块
- utils: 工具模块
"""

__version__ = "1.0.0"
__author__ = "NSFW Dataset Team"
__email__ = "<EMAIL>"

# 导入主要模块
from . import scrapers
from . import data_processing
from . import analysis
from . import utils

__all__ = [
    "scrapers",
    "data_processing", 
    "analysis",
    "utils"
]
