#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小说爬虫 - 用于爬取xchina.co网站的小说内容
用于创建内容安全风控模型的数据集
"""

import requests
import os
import time
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NovelScraper:
    def __init__(self, base_url="https://xchina.co/fictions.html"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
        self.dataset_dir = "dataset"
        
        # 创建数据集目录
        if not os.path.exists(self.dataset_dir):
            os.makedirs(self.dataset_dir)
    
    def get_novel_links(self):
        """获取所有小说链接"""
        try:
            # 添加延迟和重试机制
            time.sleep(2)
            response = self.session.get(self.base_url, timeout=15, allow_redirects=True)
            
            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 403:
                logger.warning("收到403错误，尝试使用不同的请求头")
                # 尝试更简单的请求头
                self.session.headers.clear()
                self.session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/6.0)'
                })
                time.sleep(3)
                response = self.session.get(self.base_url, timeout=15)
            
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找所有小说链接
            novel_links = []
            
            # 查找所有可能的小说链接
            for link in soup.find_all('a', href=True):
                href = link['href']
                # 过滤出小说页面链接
                if href and ('/fiction/' in href or '/novel/' in href or href.endswith('.html')):
                    full_url = urljoin(self.base_url, href)
                    if full_url not in novel_links:
                        novel_links.append(full_url)
            
            # 如果没有找到特定格式的链接，尝试查找所有内部链接
            if not novel_links:
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    if href.startswith('/') or 'xchina.co' in href:
                        full_url = urljoin(self.base_url, href)
                        # 排除主页和分类页面
                        if full_url != self.base_url and 'fictions.html' not in full_url:
                            novel_links.append(full_url)
            
            logger.info(f"找到 {len(novel_links)} 个小说链接")
            return novel_links
            
        except Exception as e:
            logger.error(f"获取小说链接失败: {e}")
            return []
    
    def extract_novel_content(self, url):
        """提取单个小说的内容"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 尝试提取标题
            title = ""
            title_selectors = ['h1', 'h2', '.title', '#title', '.novel-title']
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    title = title_elem.get_text().strip()
                    break
            
            if not title:
                title = soup.title.get_text().strip() if soup.title else "未知标题"
            
            # 清理标题，移除非法字符
            title = re.sub(r'[<>:"/\\|?*]', '', title)
            
            # 尝试提取正文内容
            content = ""
            content_selectors = [
                '.content', '#content', '.novel-content', '.story-content',
                '.main-content', 'article', '.post-content', 'main'
            ]
            
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    content = content_elem.get_text().strip()
                    break
            
            # 如果没有找到特定的内容区域，尝试提取body中的文本
            if not content:
                body = soup.find('body')
                if body:
                    # 移除脚本和样式标签
                    for script in body(["script", "style"]):
                        script.decompose()
                    content = body.get_text().strip()
            
            # 清理内容
            content = re.sub(r'\s+', ' ', content)  # 合并多个空白字符
            content = content.replace('\n', '\n\n')  # 保持段落分隔
            
            return title, content
            
        except Exception as e:
            logger.error(f"提取小说内容失败 {url}: {e}")
            return None, None
    
    def save_novel(self, title, content, index):
        """保存小说到文件"""
        if not content or len(content.strip()) < 100:  # 过滤太短的内容
            logger.warning(f"内容太短，跳过: {title}")
            return False
        
        # 生成文件名
        filename = f"{index:03d}_{title[:50]}.txt"  # 限制文件名长度
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)  # 移除非法字符
        
        filepath = os.path.join(self.dataset_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"标题: {title}\n")
                f.write("=" * 50 + "\n\n")
                f.write(content)
            
            logger.info(f"保存成功: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"保存文件失败 {filename}: {e}")
            return False
    
    def scrape_all_novels(self):
        """爬取所有小说"""
        logger.info("开始爬取小说...")
        
        # 获取所有小说链接
        novel_links = self.get_novel_links()
        
        if not novel_links:
            logger.error("没有找到小说链接")
            return
        
        success_count = 0
        
        for i, url in enumerate(novel_links, 1):
            logger.info(f"正在处理第 {i}/{len(novel_links)} 个小说: {url}")
            
            title, content = self.extract_novel_content(url)
            
            if title and content:
                if self.save_novel(title, content, i):
                    success_count += 1
            
            # 添加延迟，避免请求过于频繁
            time.sleep(1)
        
        logger.info(f"爬取完成！成功保存 {success_count} 个小说")

def main():
    scraper = NovelScraper()
    scraper.scrape_all_novels()

if __name__ == "__main__":
    main()