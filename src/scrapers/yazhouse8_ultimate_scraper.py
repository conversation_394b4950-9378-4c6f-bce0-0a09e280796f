#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yazhouse8 终极版小说爬虫
合并了所有版本的优点：
- 智能内容提取（improved版本的多选择器策略）
- Cloudflare验证处理（standalone版本的反爬虫机制）
- 强大的内容清理和质量控制
- 完善的错误处理和日志记录
- 灵活的配置和扩展性
"""

import asyncio
import os
import re
import json
import logging
import random
from pathlib import Path
from urllib.parse import urljoin, urlparse
from playwright.async_api import async_playwright
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('yazhouse8_ultimate.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Yazhouse8UltimateScraper:
    def __init__(self, output_dir="yazhouse8_novels_ultimate"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.scraped_urls = set()
        self.metadata = []
        
        # 网站配置
        self.site_config = {
            'base_url': 'https://yazhouse8.com',
            'categories': [
                '/l9kdK.htm?set',  # 丝袜美腿
                '/Ryuid.htm?set',  # 乱伦小说
                '/KGl2i.htm?set'   # 强奸小说
            ],
            'novel_selectors': [
                'a[href*="article/"]',  # improved版本发现的有效选择器
                'a[href*=".htm"]',
                '.story-link a',
                '.novel-item a',
                'td a[href*=".htm"]'
            ],
            'content_selectors': [
                '.story-content',
                '.novel-content', 
                '.content',
                'div[class*="content"]',
                'div[class*="story"]',
                'div[class*="text"]',
                '.article-content',
                '.post-content',
                'main .content',
                'article',
                '.entry-content'
            ],
            'title_selectors': [
                'h1',
                '.story-title',
                '.novel-title',
                'title'
            ]
        }
        
        # 用户代理列表
        self.user_agents = [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
    
    def clean_filename(self, filename):
        """清理文件名，移除非法字符"""
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'\s+', '_', filename)
        if len(filename) > 100:
            filename = filename[:100]
        return filename
    
    def clean_title(self, title):
        """清理标题"""
        if not title:
            return "未知标题"
        
        # 移除序号和网站信息
        title = re.sub(r'^\d+、', '', title)
        title = title.replace(' - yazhouse8', '').replace('- yazhouse8', '').strip()
        # 移除多余空白
        title = re.sub(r'\s+', ' ', title).strip()
        
        return title if title else "未知标题"
    
    async def setup_browser_context(self, browser):
        """设置浏览器上下文，模拟真实用户行为"""
        context = await browser.new_context(
            user_agent=random.choice(self.user_agents),
            viewport={'width': 1920, 'height': 1080},
            locale='zh-CN',
            timezone_id='Asia/Shanghai',
            extra_http_headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        )
        return context
    
    async def random_delay(self, min_seconds=1, max_seconds=3):
        """随机延迟，模拟人类行为"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def wait_for_cloudflare_verification(self, page, max_wait_time=30):
        """检测并等待Cloudflare验证完成"""
        try:
            cloudflare_indicators = [
                'div[class*="cf-"]',
                'div[id*="cf-"]',
                'div:has-text("Checking your browser")',
                'div:has-text("Please wait")',
                'div:has-text("Verifying you are human")',
                'div:has-text("DDoS protection")',
                'div:has-text("Just a moment")',
                'div:has-text("Please enable JavaScript")',
                'div.cf-error-details',
                'div#cf-wrapper',
                'div.cf-browser-verification'
            ]
            
            is_cloudflare_page = False
            for indicator in cloudflare_indicators:
                try:
                    element = await page.query_selector(indicator)
                    if element:
                        is_cloudflare_page = True
                        logger.info(f"检测到Cloudflare验证页面，指示器: {indicator}")
                        break
                except:
                    continue
            
            if is_cloudflare_page:
                logger.info(f"等待Cloudflare验证完成，最大等待时间: {max_wait_time}秒")
                
                start_time = asyncio.get_event_loop().time()
                while True:
                    current_time = asyncio.get_event_loop().time()
                    if current_time - start_time > max_wait_time:
                        logger.warning("Cloudflare验证等待超时")
                        break
                    
                    still_verifying = False
                    for indicator in cloudflare_indicators:
                        try:
                            element = await page.query_selector(indicator)
                            if element:
                                is_visible = await element.is_visible()
                                if is_visible:
                                    still_verifying = True
                                    break
                        except:
                            continue
                    
                    if not still_verifying:
                        logger.info("Cloudflare验证完成")
                        await asyncio.sleep(2)
                        await page.wait_for_load_state('networkidle')
                        break
                    
                    await asyncio.sleep(1)
            else:
                logger.debug("未检测到Cloudflare验证页面")
                
        except Exception as e:
            logger.warning(f"Cloudflare验证检测失败: {e}")
    
    def is_valid_novel_link(self, href):
        """验证链接是否为有效的小说链接"""
        if not href:
            return False
        
        # 排除分类页面和其他非小说页面
        invalid_patterns = [
            '/l9kdK.htm',
            '/Ryuid.htm',
            '/KGl2i.htm',
            '?set',
            'javascript:',
            'mailto:',
            '#'
        ]
        
        for pattern in invalid_patterns:
            if pattern in href:
                return False
        
        return '.htm' in href
    
    async def get_novel_links_from_category(self, page, category_url, max_novels=20):
        """从指定分类页面获取小说链接"""
        novel_links = []
        
        try:
            logger.info(f"正在访问分类页面: {category_url}")
            await page.goto(category_url, wait_until='networkidle', timeout=60000)
            await self.random_delay(3, 6)
            
            await page.wait_for_load_state('domcontentloaded')
            await page.wait_for_load_state('networkidle')
            
            await self.wait_for_cloudflare_verification(page)
            
            # 尝试不同的选择器获取小说链接
            for selector in self.site_config['novel_selectors']:
                try:
                    links = await page.query_selector_all(selector)
                    if links:
                        logger.debug(f"使用选择器 '{selector}' 找到 {len(links)} 个链接")
                        
                        for link in links:
                            try:
                                href = await link.get_attribute('href')
                                title = await link.inner_text()
                                
                                if href and title and len(title.strip()) > 3:
                                    if href.startswith('http'):
                                        full_url = href
                                    else:
                                        full_url = urljoin(self.site_config['base_url'], href)
                                    
                                    if (self.is_valid_novel_link(href) and 
                                        full_url not in self.scraped_urls and 
                                        full_url not in [item['url'] for item in novel_links]):
                                        
                                        clean_title = self.clean_title(title)
                                        novel_links.append({
                                            'title': clean_title,
                                            'url': full_url,
                                            'category_url': category_url
                                        })
                                        
                                        if len(novel_links) >= max_novels:
                                            break
                            except Exception as e:
                                logger.warning(f"处理链接时出错: {e}")
                                continue
                        
                        if novel_links:
                            logger.info(f"选择器 '{selector}' 成功找到 {len(novel_links)} 个有效链接")
                            break
                except Exception as e:
                    logger.debug(f"选择器 '{selector}' 失败: {e}")
                    continue
            
            logger.info(f"从分类 {category_url} 获取到 {len(novel_links)} 个小说链接")
            return novel_links[:max_novels]
            
        except Exception as e:
            logger.error(f"获取分类 {category_url} 的小说链接失败: {e}")
            return []
    
    async def extract_novel_content(self, page, novel_info):
        """提取小说内容 - 使用improved版本的智能提取策略"""
        url = novel_info['url']
        title = novel_info['title']
        
        if url in self.scraped_urls:
            logger.info(f"跳过已爬取的URL: {url}")
            return None
            
        logger.info(f"正在提取小说内容: {title} - {url}")
        
        try:
            await page.goto(url, wait_until='domcontentloaded', timeout=30000)
            await self.random_delay(2, 4)
            
            await self.wait_for_cloudflare_verification(page)
            
            # 获取页面标题（如果原标题不够好）
            page_title = title
            for selector in self.site_config['title_selectors']:
                try:
                    title_element = await page.query_selector(selector)
                    if title_element:
                        title_text = await title_element.inner_text()
                        if title_text:
                            cleaned_title = self.clean_title(title_text)
                            if cleaned_title and cleaned_title != "未知标题" and len(cleaned_title) > len(title):
                                page_title = cleaned_title
                                break
                except:
                    continue
            
            # 尝试不同的内容选择器
            content = ""
            used_selector = None
            
            for selector in self.site_config['content_selectors']:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        text = await element.inner_text()
                        # 检查内容质量
                        if (len(text) > 500 and 
                            '404' not in text.lower() and 
                            'not found' not in text.lower() and
                            '广告' not in text[:100] and
                            '导航' not in text[:100]):
                            content = text
                            used_selector = selector
                            logger.info(f"使用选择器 '{selector}' 提取到内容，长度: {len(content)}")
                            break
                except Exception as e:
                    continue
            
            # 如果没有找到专门的内容区域，尝试智能提取
            if not content:
                logger.info(f"尝试智能提取内容: {title}")
                
                # 获取页面所有段落
                paragraphs = await page.query_selector_all('p')
                content_paragraphs = []
                
                for p in paragraphs:
                    try:
                        text = await p.inner_text()
                        text = text.strip()
                        
                        # 过滤掉导航、广告等内容
                        if (len(text) > 20 and 
                            not any(keyword in text.lower() for keyword in 
                                   ['导航', '广告', '首页', '下一页', '上一页', '返回', 
                                    'copyright', '版权', 'app下载', '联系我们'])):
                            content_paragraphs.append(text)
                    except:
                        continue
                
                if content_paragraphs:
                    content = '\n\n'.join(content_paragraphs)
                    used_selector = 'smart_extraction'
            
            # 最后尝试：获取body内容并清理
            if not content or len(content) < 200:
                logger.info(f"尝试从body提取内容: {title}")
                
                body_element = await page.query_selector('body')
                if body_element:
                    body_text = await body_element.inner_text()
                    
                    # 按行分割并过滤
                    lines = body_text.split('\n')
                    content_lines = []
                    
                    for line in lines:
                        line = line.strip()
                        if (len(line) > 15 and 
                            not any(keyword in line.lower() for keyword in 
                                   ['导航', '广告', '首页', '下一页', '上一页', '返回',
                                    'app', 'copyright', '版权', '联系', '合作'])):
                            content_lines.append(line)
                    
                    if content_lines:
                        content = '\n'.join(content_lines)
                        used_selector = 'body_filtered'
            
            # 清理内容
            if content:
                content = self.clean_content(content)
            
            if content and len(content) > 200:
                self.scraped_urls.add(url)
                
                return {
                    'title': page_title,
                    'content': content,
                    'url': url,
                    'length': len(content),
                    'source': self.site_config['base_url'],
                    'selector_used': used_selector,
                    'category_url': novel_info.get('category_url', '')
                }
            else:
                logger.warning(f"提取的内容太短或为空: {title} (长度: {len(content) if content else 0})")
                return None
                
        except Exception as e:
            logger.error(f"提取小说内容时出错 {url}: {e}")
            return None
    
    def clean_content(self, content):
        """清理小说内容"""
        if not content:
            return ""
        
        # 移除多余的空白字符
        content = re.sub(r'\n\s*\n', '\n\n', content)
        content = re.sub(r'[ \t]+', ' ', content)
        
        # 移除常见的网站信息
        patterns_to_remove = [
            r'yazhouse8\.com',
            r'本站地址：.*',
            r'更新时间：.*',
            r'字数：.*',
            r'点击：.*次',
            r'收藏：.*',
            r'推荐票：.*',
            r'上一章.*下一章',
            r'返回目录.*',
            r'加入书签.*',
            r'举报.*',
        ]
        
        for pattern in patterns_to_remove:
            content = re.sub(pattern, '', content, flags=re.IGNORECASE)
        
        return content.strip()
    
    def classify_content(self, content):
        """简单的内容分类"""
        # 由于是成人内容网站，默认分类为NSFW
        return 'NSFW'
    
    def save_novel(self, novel_data):
        """保存小说到文件"""
        try:
            category = self.classify_content(novel_data['content'])
            
            # 生成唯一文件名
            clean_title = self.clean_filename(novel_data['title'])
            filename = f"{category}_{clean_title}.txt"
            
            # 确保文件名唯一
            counter = 1
            original_filename = filename
            while (self.output_dir / filename).exists():
                name, ext = original_filename.rsplit('.', 1)
                filename = f"{name}_{counter}.{ext}"
                counter += 1
            
            filepath = self.output_dir / filename
            
            # 保存文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"标题: {novel_data['title']}\n")
                f.write(f"来源: {novel_data['url']}\n")
                f.write(f"网站: {novel_data['source']}\n")
                f.write(f"分类页面: {novel_data.get('category_url', '')}\n")
                f.write(f"长度: {novel_data['length']} 字符\n")
                f.write(f"提取方式: {novel_data.get('selector_used', 'unknown')}\n")
                f.write(f"爬取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 50 + "\n\n")
                f.write(novel_data['content'])
            
            # 添加到元数据
            self.metadata.append({
                'filename': filename,
                'title': novel_data['title'],
                'url': novel_data['url'],
                'source': novel_data['source'],
                'category': category,
                'category_url': novel_data.get('category_url', ''),
                'length': novel_data['length'],
                'selector_used': novel_data.get('selector_used', 'unknown'),
                'scraped_at': datetime.now().isoformat()
            })
            
            logger.info(f"已保存小说: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"保存小说时出错: {e}")
            return False
    
    def save_metadata(self):
        """保存元数据"""
        metadata = {
            'total_novels': len(self.metadata),
            'scraped_at': datetime.now().isoformat(),
            'source': self.site_config['base_url'],
            'scraper_version': 'ultimate',
            'novels': self.metadata
        }
        
        with open(self.output_dir / 'scraped_metadata.json', 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        logger.info(f"元数据已保存，共 {len(self.metadata)} 部小说")
    
    async def run(self, max_novels_per_category=10, headless=True):
        """运行爬虫"""
        logger.info("开始运行 Yazhouse8 终极版爬虫")
        logger.info(f"输出目录: {self.output_dir}")
        logger.info(f"每个分类最大小说数: {max_novels_per_category}")
        logger.info(f"无头模式: {headless}")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=headless,
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            try:
                context = await self.setup_browser_context(browser)
                page = await context.new_page()
                
                # 设置反检测措施
                await page.add_init_script("""
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });
                """)
                
                all_novel_links = []
                
                # 遍历每个分类
                for category in self.site_config['categories']:
                    category_url = f"{self.site_config['base_url']}{category}"
                    
                    try:
                        category_links = await self.get_novel_links_from_category(
                            page, category_url, max_novels_per_category
                        )
                        all_novel_links.extend(category_links)
                        
                        logger.info(f"分类 {category} 获取到 {len(category_links)} 个链接")
                        
                        # 分类间延迟
                        await self.random_delay(3, 6)
                        
                    except Exception as e:
                        logger.error(f"处理分类 {category} 时出错: {e}")
                        continue
                
                if not all_novel_links:
                    logger.error("未找到任何小说链接")
                    return
                
                logger.info(f"总共找到 {len(all_novel_links)} 个小说链接，开始提取内容...")
                
                # 爬取每个小说
                success_count = 0
                for i, novel_info in enumerate(all_novel_links, 1):
                    logger.info(f"进度: {i}/{len(all_novel_links)} - {novel_info['title']}")
                    
                    try:
                        novel_data = await self.extract_novel_content(page, novel_info)
                        if novel_data and self.save_novel(novel_data):
                            success_count += 1
                            logger.info(f"成功爬取第 {success_count} 部小说: {novel_data['title']}")
                        
                        # 随机延迟，避免被封
                        await self.random_delay(3, 6)
                        
                    except Exception as e:
                        logger.error(f"处理小说失败 {novel_info['title']}: {e}")
                        continue
                
                # 保存元数据
                self.save_metadata()
                
                logger.info(f"\n爬取完成！")
                logger.info(f"成功爬取: {success_count} 个小说")
                logger.info(f"总计保存: {len(self.metadata)} 个小说")
                
                # 统计信息
                categories_stats = {}
                for novel in self.metadata:
                    cat_url = novel.get('category_url', '未知分类')
                    categories_stats[cat_url] = categories_stats.get(cat_url, 0) + 1
                
                logger.info("\n分类统计:")
                for cat, count in categories_stats.items():
                    logger.info(f"  {cat}: {count} 部小说")
                
            finally:
                await browser.close()

def main():
    """主函数"""
    scraper = Yazhouse8UltimateScraper()
    
    try:
        # 每个分类爬取10个小说，使用有头模式便于调试
        asyncio.run(scraper.run(max_novels_per_category=10, headless=False))
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
    except Exception as e:
        logger.error(f"爬取过程中发生错误: {e}")

if __name__ == "__main__":
    main()