#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XChina 专用小说爬虫 - 使用Playwright浏览器自动化
支持JavaScript渲染页面和反爬虫机制绕过
"""

import asyncio
import os
import re
import json
import logging
import random
from pathlib import Path
from urllib.parse import urljoin, urlparse
from playwright.async_api import async_playwright
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('xchina_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class XChinaScraper:
    def __init__(self, output_dir="xchina_novels"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.scraped_urls = set()
        self.metadata = []
        
        # XChina 网站配置
        self.site_config = {
            'base_url': 'https://xchina.co',
            'fiction_page': '/fictions.html',
            'novel_selectors': [
                'a[href*="/fiction/"]',
                '.fiction-item a',
                '.novel-link'
            ],
            'content_selectors': [
                'div.article.fiction',
                '.fiction-content',
                '.novel-content'
            ],
            'title_selectors': [
                'h1',
                '.fiction-title',
                '.novel-title'
            ],
            'next_page_selectors': [
                'a[href*="page="]:contains("下一页")',
                'a[href*="page="]:contains("Next")',
                '.pagination a:last-child',
                'a.next'
            ]
        }
        
        # 用户代理列表
        self.user_agents = [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
    
    def clean_filename(self, filename):
        """清理文件名，移除非法字符"""
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'\s+', '_', filename)
        if len(filename) > 100:
            filename = filename[:100]
        return filename
    
    async def setup_browser_context(self, browser):
        """设置浏览器上下文，模拟真实用户行为"""
        context = await browser.new_context(
            user_agent=random.choice(self.user_agents),
            viewport={'width': 1920, 'height': 1080},
            locale='zh-CN',
            timezone_id='Asia/Shanghai',
            extra_http_headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        )
        return context
    
    async def random_delay(self, min_seconds=1, max_seconds=3):
        """随机延迟，模拟人类行为"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def wait_for_cloudflare_verification(self, page, max_wait_time=30):
        """检测并等待Cloudflare验证完成"""
        try:
            cloudflare_indicators = [
                'div[class*="cf-"]',
                'div[id*="cf-"]',
                'div:has-text("Checking your browser")',
                'div:has-text("Please wait")',
                'div:has-text("Verifying you are human")',
                'div:has-text("DDoS protection")',
                'div:has-text("Just a moment")',
                'div:has-text("Please enable JavaScript")',
                'div.cf-error-details',
                'div#cf-wrapper',
                'div.cf-browser-verification'
            ]
            
            is_cloudflare_page = False
            for indicator in cloudflare_indicators:
                try:
                    element = await page.query_selector(indicator)
                    if element:
                        is_cloudflare_page = True
                        logger.info(f"检测到Cloudflare验证页面，指示器: {indicator}")
                        break
                except:
                    continue
            
            if is_cloudflare_page:
                logger.info(f"等待Cloudflare验证完成，最大等待时间: {max_wait_time}秒")
                
                start_time = asyncio.get_event_loop().time()
                while True:
                    current_time = asyncio.get_event_loop().time()
                    if current_time - start_time > max_wait_time:
                        logger.warning("Cloudflare验证等待超时")
                        break
                    
                    still_verifying = False
                    for indicator in cloudflare_indicators:
                        try:
                            element = await page.query_selector(indicator)
                            if element:
                                is_visible = await element.is_visible()
                                if is_visible:
                                    still_verifying = True
                                    break
                        except:
                            continue
                    
                    if not still_verifying:
                        logger.info("Cloudflare验证完成")
                        await asyncio.sleep(2)
                        await page.wait_for_load_state('networkidle')
                        break
                    
                    await asyncio.sleep(1)
            else:
                logger.debug("未检测到Cloudflare验证页面")
                
        except Exception as e:
            logger.warning(f"Cloudflare验证检测失败: {e}")
    
    async def get_novel_links(self, page, max_novels=50):
        """获取小说链接列表，支持翻页"""
        all_novel_links = []
        current_page = 1
        max_pages = 10  # 限制最大翻页数
        
        try:
            while len(all_novel_links) < max_novels and current_page <= max_pages:
                # 构建当前页面URL
                if current_page == 1:
                    page_url = f"{self.site_config['base_url']}{self.site_config['fiction_page']}"
                else:
                    page_url = f"{self.site_config['base_url']}/fictions/{current_page}.html"
                
                logger.info(f"正在访问第 {current_page} 页: {page_url}")
                
                await page.goto(page_url, wait_until='networkidle', timeout=60000)
                await self.random_delay(3, 6)
                
                await page.wait_for_load_state('domcontentloaded')
                await page.wait_for_load_state('networkidle')
                
                await self.wait_for_cloudflare_verification(page)
                
                # 获取当前页面的小说链接
                page_novel_links = await self.extract_novel_links_from_page(page)
                
                if not page_novel_links:
                    logger.info(f"第 {current_page} 页没有找到小说链接，停止翻页")
                    break
                
                # 过滤已爬取的链接
                new_links = []
                for link in page_novel_links:
                    if link not in self.scraped_urls and len(all_novel_links) + len(new_links) < max_novels:
                        new_links.append(link)
                        self.scraped_urls.add(link)
                
                all_novel_links.extend(new_links)
                logger.info(f"第 {current_page} 页找到 {len(new_links)} 个新链接，总计 {len(all_novel_links)} 个")
                
                # 检查是否有下一页
                has_next_page = await self.check_next_page(page)
                if not has_next_page:
                    logger.info("没有更多页面，停止翻页")
                    break
                
                current_page += 1
                await self.random_delay(1, 3)
            
            logger.info(f"总共找到 {len(all_novel_links)} 个小说链接")
            return all_novel_links
            
        except Exception as e:
            logger.error(f"获取小说链接失败: {e}")
            return all_novel_links
    
    async def extract_novel_links_from_page(self, page):
        """从当前页面提取小说链接"""
        novel_links = []
        
        for selector in self.site_config['novel_selectors']:
            try:
                links = await page.query_selector_all(selector)
                if links:
                    logger.debug(f"使用选择器 '{selector}' 找到 {len(links)} 个链接")
                    for link in links:
                        href = await link.get_attribute('href')
                        if href:
                            if href.startswith('http'):
                                full_url = href
                            else:
                                full_url = urljoin(self.site_config['base_url'], href)
                            
                            if self.is_valid_novel_link(href) and full_url not in novel_links:
                                novel_links.append(full_url)
                    
                    if novel_links:
                        logger.debug(f"选择器 '{selector}' 成功找到 {len(novel_links)} 个有效链接")
                        break
            except Exception as e:
                logger.debug(f"选择器 '{selector}' 失败: {e}")
                continue
        
        return novel_links
    
    def is_valid_novel_link(self, href):
        """验证链接是否为有效的小说链接"""
        if not href:
            return False
        return '/fiction/id-' in href or 'fiction' in href
    
    def clean_title(self, title_text):
        """清理标题，移除网站特定的后缀"""
        if not title_text:
            return "未知标题"
        
        title = title_text.strip()
        title = title.replace(' - 小黄书 xChina', '').replace('- xChina', '').strip()
        
        return title if title else "未知标题"
    
    async def check_next_page(self, page):
        """检查是否有下一页"""
        try:
            for selector in self.site_config['next_page_selectors']:
                try:
                    next_link = await page.query_selector(selector)
                    if next_link:
                        class_attr = await next_link.get_attribute('class')
                        if class_attr and 'disabled' in class_attr:
                            continue
                        
                        href = await next_link.get_attribute('href')
                        if href:
                            logger.debug(f"找到下一页链接: {href}")
                            return True
                except Exception as e:
                    logger.debug(f"检查选择器 '{selector}' 失败: {e}")
                    continue
            
            # 检查是否有数字分页链接
            page_links = await page.query_selector_all('a[href*="page"]')
            if len(page_links) > 1:
                return True
            
            return False
            
        except Exception as e:
            logger.debug(f"检查下一页失败: {e}")
            return False
    
    async def extract_novel_content(self, page, url):
        """提取单个小说的内容"""
        try:
            logger.info(f"正在提取小说内容: {url}")
            
            await page.goto(url, wait_until='networkidle', timeout=60000)
            await self.random_delay(2, 4)
            
            await page.wait_for_load_state('domcontentloaded')
            await page.wait_for_load_state('networkidle')
            
            await self.wait_for_cloudflare_verification(page)
            
            # 获取页面标题
            title = "未知标题"
            for selector in self.site_config['title_selectors']:
                try:
                    title_element = await page.query_selector(selector)
                    if title_element:
                        title_text = await title_element.inner_text()
                        if title_text:
                            title = self.clean_title(title_text)
                            if title and title != "未知标题":
                                break
                except:
                    continue
            
            # 检查是否为漫画版
            if "（漫画版）" in title or "(漫画版)" in title:
                logger.info(f"跳过漫画版小说: {title}")
                return None
            
            # 检查是否为多章节小说
            chapters_element = await page.query_selector('div.chapters')
            if chapters_element:
                logger.info(f"检测到多章节小说: {title}")
                return await self.extract_multi_chapter_content(page, url, title)
            
            # 单章节小说处理
            content = ""
            try:
                # 首先尝试精确的选择器
                article_element = await page.query_selector('div.article.fiction')
                if article_element:
                    content = await article_element.inner_text()
                    logger.info(f"使用 div.article.fiction 选择器提取到内容，长度: {len(content)}")
                else:
                    # 尝试其他选择器
                    for selector in self.site_config['content_selectors']:
                        try:
                            element = await page.query_selector(selector)
                            if element:
                                temp_content = await element.inner_text()
                                if temp_content and len(temp_content.strip()) > 100:
                                    content = temp_content
                                    logger.info(f"使用选择器 '{selector}' 提取到内容，长度: {len(content)}")
                                    break
                        except:
                            continue
            except Exception as e:
                logger.warning(f"提取正文内容失败: {e}")
            
            if not content:
                logger.warning(f"未能提取到有效内容: {url}")
                return None
            
            # 清理内容
            content = self.clean_content(content)
            
            if len(content.strip()) < 100:
                logger.warning(f"内容过短，跳过: {title}")
                return None
            
            return {
                'title': title,
                'content': content,
                'url': url,
                'length': len(content),
                'scraped_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"提取小说内容失败 {url}: {e}")
            return None
    
    async def extract_multi_chapter_content(self, page, url, title):
        """提取多章节小说内容"""
        try:
            logger.info(f"开始提取多章节小说: {title}")
            
            # 获取章节链接
            chapter_links = await page.query_selector_all('div.chapters a')
            if not chapter_links:
                logger.warning(f"未找到章节链接: {title}")
                return None
            
            total_chapters = len(chapter_links)
            max_chapters = min(50, total_chapters)  # 限制最大章节数
            
            logger.info(f"发现 {total_chapters} 个章节，将提取前 {max_chapters} 个章节")
            
            all_content = []
            
            for i in range(max_chapters):
                try:
                    chapter_link = chapter_links[i]
                    chapter_href = await chapter_link.get_attribute('href')
                    chapter_title = await chapter_link.inner_text()
                    
                    if chapter_href:
                        if chapter_href.startswith('http'):
                            chapter_url = chapter_href
                        else:
                            chapter_url = urljoin(self.site_config['base_url'], chapter_href)
                        
                        logger.info(f"提取第 {i+1}/{max_chapters} 章: {chapter_title}")
                        
                        # 访问章节页面
                        await page.goto(chapter_url, wait_until='networkidle', timeout=60000)
                        await self.random_delay(1, 3)
                        
                        # 提取章节内容
                        chapter_content = ""
                        article_element = await page.query_selector('div.article.fiction')
                        if article_element:
                            chapter_content = await article_element.inner_text()
                        
                        if chapter_content:
                            all_content.append(f"\n\n=== {chapter_title} ===\n\n{chapter_content}")
                        
                        await self.random_delay(1, 2)
                        
                except Exception as e:
                    logger.warning(f"提取第 {i+1} 章失败: {e}")
                    continue
            
            if not all_content:
                logger.warning(f"未能提取到任何章节内容: {title}")
                return None
            
            # 合并所有章节内容
            full_content = '\n'.join(all_content)
            full_content = self.clean_content(full_content)
            
            return {
                'title': title,
                'content': full_content,
                'url': url,
                'length': len(full_content),
                'chapters': len(all_content),
                'scraped_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"提取多章节小说失败 {url}: {e}")
            return None
    
    def clean_content(self, content):
        """清理小说内容"""
        if not content:
            return ""
        
        # 移除多余的空白字符
        content = re.sub(r'\n\s*\n', '\n\n', content)
        content = re.sub(r'[ \t]+', ' ', content)
        
        # 移除常见的网站信息
        patterns_to_remove = [
            r'xchina\.co',
            r'小黄书',
            r'本站地址：.*',
            r'更新时间：.*',
            r'字数：.*',
            r'点击：.*次',
            r'收藏：.*',
            r'推荐票：.*',
            r'上一章.*下一章',
            r'返回目录.*',
            r'加入书签.*',
            r'举报.*',
        ]
        
        for pattern in patterns_to_remove:
            content = re.sub(pattern, '', content, flags=re.IGNORECASE)
        
        return content.strip()
    
    def save_novel(self, novel_data):
        """保存小说到文件"""
        try:
            # 生成文件名
            clean_title = self.clean_filename(novel_data['title'])
            filename = f"NSFW_{clean_title}.txt"
            filepath = self.output_dir / filename
            
            # 准备文件内容
            file_content = f"""标题: {novel_data['title']}
来源: {novel_data['url']}
长度: {novel_data['length']} 字符
爬取时间: {novel_data['scraped_at']}
分类: NSFW
"""
            
            if 'chapters' in novel_data:
                file_content += f"章节数: {novel_data['chapters']}\n"
            
            file_content += f"\n{'='*50}\n\n{novel_data['content']}"
            
            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(file_content)
            
            # 添加到元数据
            metadata_entry = {
                'filename': filename,
                'title': novel_data['title'],
                'url': novel_data['url'],
                'category': 'NSFW',
                'length': novel_data['length'],
                'scraped_at': novel_data['scraped_at']
            }
            
            if 'chapters' in novel_data:
                metadata_entry['chapters'] = novel_data['chapters']
            
            self.metadata.append(metadata_entry)
            
            logger.info(f"保存小说成功: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"保存小说失败: {e}")
            return False
    
    def save_metadata(self):
        """保存元数据到JSON文件"""
        try:
            metadata_file = self.output_dir / 'scraped_metadata.json'
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
            logger.info(f"元数据保存成功: {metadata_file}")
        except Exception as e:
            logger.error(f"保存元数据失败: {e}")
    
    async def run(self, max_novels=30):
        """运行爬虫"""
        logger.info("开始运行 XChina 专用爬虫")
        logger.info(f"输出目录: {self.output_dir}")
        logger.info(f"最大小说数: {max_novels}")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=False,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            try:
                context = await self.setup_browser_context(browser)
                page = await context.new_page()
                
                # 设置反检测措施
                await page.add_init_script("""
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });
                """)
                
                # 获取小说链接
                novel_links = await self.get_novel_links(page, max_novels)
                
                if not novel_links:
                    logger.error("未找到任何小说链接")
                    return
                
                logger.info(f"总共找到 {len(novel_links)} 个小说链接，开始提取内容...")
                
                # 爬取每个小说
                success_count = 0
                for i, url in enumerate(novel_links, 1):
                    logger.info(f"进度: {i}/{len(novel_links)} - {url}")
                    
                    try:
                        novel_data = await self.extract_novel_content(page, url)
                        if novel_data and self.save_novel(novel_data):
                            success_count += 1
                        
                        # 随机延迟，避免被封
                        await self.random_delay(2, 5)
                        
                    except Exception as e:
                        logger.error(f"处理小说失败 {url}: {e}")
                        continue
                
                # 保存元数据
                self.save_metadata()
                
                logger.info(f"爬取完成！成功爬取 {success_count} 个小说")
                logger.info(f"总计: {len(self.metadata)} 个小说")
                
            finally:
                await browser.close()

def main():
    """主函数"""
    scraper = XChinaScraper()
    
    try:
        # 爬取30个小说
        asyncio.run(scraper.run(max_novels=30))
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
    except Exception as e:
        logger.error(f"爬取过程中发生错误: {e}")

if __name__ == "__main__":
    main()