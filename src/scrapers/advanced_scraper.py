#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级小说爬虫 - 使用Playwright浏览器自动化
支持JavaScript渲染页面和反爬虫机制绕过
"""

import asyncio
import os
import re
import json
import logging
import random
from pathlib import Path
from urllib.parse import urljoin, urlparse
from playwright.async_api import async_playwright
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AdvancedNovelScraper:
    def __init__(self, output_dir="dataset"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.scraped_urls = set()
        self.metadata = []
        
        # 网站配置
        self.sites_config = {
            'xchina': {
                'base_url': 'https://xchina.co',
                'fiction_page': '/fictions.html',
                'novel_selectors': [
                    'a[href*="/fiction/"]',
                    '.fiction-item a',
                    '.novel-link'
                ],
                'content_selectors': [
                    'div.article.fiction',
                    '.fiction-content',
                    '.novel-content'
                ],
                'title_selectors': [
                    'h1',
                    '.fiction-title',
                    '.novel-title'
                ],
                'next_page_selectors': [
                    'a[href*="page="]:contains("下一页")',
                    'a[href*="page="]:contains("Next")',
                    '.pagination a:last-child',
                    'a.next'
                ]
            },
            'yazhouse8': {
                'base_url': 'https://yazhouse8.com',
                'fiction_page': '/l9kdK.htm?set',
                'novel_selectors': [
                    'a[href*=".htm"]',
                    '.story-link a',
                    '.novel-item a',
                    'td a[href*=".htm"]'
                ],
                'content_selectors': [
                    '.story-content',
                    '.novel-text',
                    '.content',
                    'div[class*="content"]',
                    'div[class*="story"]'
                ],
                'title_selectors': [
                    'h1',
                    '.story-title',
                    '.novel-title',
                    'title'
                ],
                'next_page_selectors': [
                    'a[href*="page"]:contains("下一页")',
                    'a[href*="page"]:contains("Next")',
                    '.pagination a:last-child',
                    'a.next'
                ]
            }
        }
        
        # 用户代理列表
        self.user_agents = [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
    
    def clean_filename(self, filename):
        """清理文件名，移除非法字符"""
        # 移除或替换非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'\s+', '_', filename)
        # 限制长度
        if len(filename) > 100:
            filename = filename[:100]
        return filename
    
    async def setup_browser_context(self, browser):
        """设置浏览器上下文，模拟真实用户行为"""
        context = await browser.new_context(
            user_agent=random.choice(self.user_agents),
            viewport={'width': 1920, 'height': 1080},
            locale='zh-CN',
            timezone_id='Asia/Shanghai',
            extra_http_headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        )
        return context
    
    async def random_delay(self, min_seconds=1, max_seconds=3):
        """随机延迟，模拟人类行为"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def wait_for_cloudflare_verification(self, page, max_wait_time=30):
        """检测并等待Cloudflare验证完成"""
        try:
            # 检测Cloudflare验证页面的常见元素
            cloudflare_indicators = [
                'div[class*="cf-"]',  # Cloudflare相关的div
                'div[id*="cf-"]',
                'div:has-text("Checking your browser")',
                'div:has-text("Please wait")',
                'div:has-text("Verifying you are human")',
                'div:has-text("DDoS protection")',
                'div:has-text("Just a moment")',
                'div:has-text("Please enable JavaScript")',
                'div.cf-error-details',
                'div#cf-wrapper',
                'div.cf-browser-verification'
            ]
            
            # 检查是否存在Cloudflare验证页面
            is_cloudflare_page = False
            for indicator in cloudflare_indicators:
                try:
                    element = await page.query_selector(indicator)
                    if element:
                        is_cloudflare_page = True
                        logger.info(f"检测到Cloudflare验证页面，指示器: {indicator}")
                        break
                except:
                    continue
            
            if is_cloudflare_page:
                logger.info(f"等待Cloudflare验证完成，最大等待时间: {max_wait_time}秒")
                
                # 等待验证完成，通过检查页面是否不再包含Cloudflare元素
                start_time = asyncio.get_event_loop().time()
                while True:
                    current_time = asyncio.get_event_loop().time()
                    if current_time - start_time > max_wait_time:
                        logger.warning("Cloudflare验证等待超时")
                        break
                    
                    # 检查是否还有Cloudflare元素
                    still_verifying = False
                    for indicator in cloudflare_indicators:
                        try:
                            element = await page.query_selector(indicator)
                            if element:
                                # 检查元素是否可见
                                is_visible = await element.is_visible()
                                if is_visible:
                                    still_verifying = True
                                    break
                        except:
                            continue
                    
                    if not still_verifying:
                        logger.info("Cloudflare验证完成")
                        # 额外等待一下确保页面完全加载
                        await asyncio.sleep(2)
                        await page.wait_for_load_state('networkidle')
                        break
                    
                    # 短暂等待后再次检查
                    await asyncio.sleep(1)
            else:
                logger.debug("未检测到Cloudflare验证页面")
                
        except Exception as e:
            logger.warning(f"Cloudflare验证检测失败: {e}")
    
    async def get_novel_links(self, page, site_name, max_novels=50):
        """获取小说链接列表，支持翻页"""
        site_config = self.sites_config[site_name]
        all_novel_links = []
        current_page = 1
        max_pages = 10  # 限制最大翻页数，避免无限循环
        
        try:
            while len(all_novel_links) < max_novels and current_page <= max_pages:
                # 构建当前页面URL
                if current_page == 1:
                    page_url = f"{site_config['base_url']}{site_config['fiction_page']}"
                else:
                    # 不同网站的分页URL格式可能不同
                    if site_name == 'xchina':
                        page_url = f"{site_config['base_url']}/fictions/{current_page}.html"
                    elif site_name == 'yazhouse8':
                        page_url = f"{site_config['base_url']}/l9kdK.htm?set&page={current_page}"
                    else:
                        page_url = f"{site_config['base_url']}{site_config['fiction_page']}?page={current_page}"
                
                logger.info(f"正在访问第 {current_page} 页: {page_url}")
                
                # 访问当前页面
                await page.goto(page_url, wait_until='networkidle', timeout=60000)
                await self.random_delay(3, 6)
                
                # 等待页面加载完成
                await page.wait_for_load_state('domcontentloaded')
                await page.wait_for_load_state('networkidle')
                
                # 检测并等待Cloudflare验证
                await self.wait_for_cloudflare_verification(page)
                
                # 获取当前页面的小说链接
                page_novel_links = await self.extract_novel_links_from_page(page, site_config)
                
                if not page_novel_links:
                    logger.info(f"第 {current_page} 页没有找到小说链接，停止翻页")
                    break
                
                # 过滤已爬取的链接
                new_links = []
                for link in page_novel_links:
                    if link not in self.scraped_urls and len(all_novel_links) + len(new_links) < max_novels:
                        new_links.append(link)
                        self.scraped_urls.add(link)
                
                all_novel_links.extend(new_links)
                logger.info(f"第 {current_page} 页找到 {len(new_links)} 个新链接，总计 {len(all_novel_links)} 个")
                
                # 检查是否有下一页
                has_next_page = await self.check_next_page(page, site_config)
                if not has_next_page:
                    logger.info("没有更多页面，停止翻页")
                    break
                
                current_page += 1
                await self.random_delay(1, 3)  # 翻页间隔
            
            logger.info(f"总共找到 {len(all_novel_links)} 个小说链接")
            return all_novel_links
            
        except Exception as e:
            logger.error(f"获取小说链接失败: {e}")
            return all_novel_links
    
    async def extract_novel_links_from_page(self, page, site_config):
        """从当前页面提取小说链接"""
        novel_links = []
        base_url = site_config['base_url']
        selectors = site_config['novel_selectors']
        
        for selector in selectors:
            try:
                links = await page.query_selector_all(selector)
                if links:
                    logger.debug(f"使用选择器 '{selector}' 找到 {len(links)} 个链接")
                    for link in links:
                        href = await link.get_attribute('href')
                        if href:
                            # 构建完整URL
                            if href.startswith('http'):
                                full_url = href
                            else:
                                full_url = urljoin(base_url, href)
                            
                            # 验证链接是否有效
                            if self.is_valid_novel_link(href, base_url):
                                if full_url not in novel_links:
                                    novel_links.append(full_url)
                    
                    if novel_links:
                        logger.debug(f"选择器 '{selector}' 成功找到 {len(novel_links)} 个有效链接")
                        break
            except Exception as e:
                logger.debug(f"选择器 '{selector}' 失败: {e}")
                continue
        
        # 如果没有找到链接，尝试获取页面上所有链接
        if not novel_links:
            logger.debug("尝试获取页面上所有链接...")
            all_links = await page.query_selector_all('a[href]')
            for link in all_links:
                href = await link.get_attribute('href')
                if href and self.is_valid_novel_link(href, base_url):
                    if href.startswith('http'):
                        full_url = href
                    else:
                        full_url = urljoin(base_url, href)
                    if full_url not in novel_links:
                        novel_links.append(full_url)
        
        return novel_links
    
    def is_valid_novel_link(self, href, base_url):
        """验证链接是否为有效的小说链接"""
        if not href:
            return False
        
        # 根据不同网站的URL模式进行验证
        if 'xchina.co' in base_url:
            return '/fiction/id-' in href or 'fiction' in href
        elif 'yazhouse8.com' in base_url:
            return '.htm' in href and href != '/l9kdK.htm?set'
        
        return True
    
    def clean_title(self, title_text, base_url):
        """清理标题，移除网站特定的后缀"""
        if not title_text:
            return "未知标题"
        
        title = title_text.strip()
        
        # 根据不同网站清理标题
        if 'xchina.co' in base_url:
            title = title.replace(' - 小黄书 xChina', '').replace('- xChina', '').strip()
        elif 'yazhouse8.com' in base_url:
            # 移除yazhouse8网站可能的后缀
            title = title.replace(' - yazhouse8', '').replace('- yazhouse8', '').strip()
        
        return title if title else "未知标题"
    
    async def check_next_page(self, page, site_config):
        """检查是否有下一页"""
        try:
            # 使用配置的下一页选择器
            pager_selectors = site_config['next_page_selectors']
            
            for selector in pager_selectors:
                try:
                    next_link = await page.query_selector(selector)
                    if next_link:
                        # 检查链接是否可用（不是禁用状态）
                        class_attr = await next_link.get_attribute('class')
                        if class_attr and 'disabled' in class_attr:
                            continue
                        
                        href = await next_link.get_attribute('href')
                        if href:
                            logger.debug(f"找到下一页链接: {href}")
                            return True
                except Exception as e:
                    logger.debug(f"检查选择器 '{selector}' 失败: {e}")
                    continue
            
            # 检查是否有数字分页链接（通用方法）
            page_links = await page.query_selector_all('a[href*="page"]')
            if len(page_links) > 1:  # 如果有多个分页链接，说明可能有下一页
                return True
            
            return False
            
        except Exception as e:
            logger.debug(f"检查下一页失败: {e}")
            return False
    
    async def extract_novel_content(self, page, url, site_config):
        """提取单个小说的内容"""
        try:
            logger.info(f"正在提取小说内容: {url}")
            
            await page.goto(url, wait_until='networkidle', timeout=60000)
            await self.random_delay(2, 4)
            
            # 等待页面加载完成
            await page.wait_for_load_state('domcontentloaded')
            await page.wait_for_load_state('networkidle')
            
            # 检测并等待Cloudflare验证
            await self.wait_for_cloudflare_verification(page)
            
            # 检查是否有反爬虫挑战按钮
            challenge_button = await page.query_selector('a[name="challenge"]')
            if challenge_button:
                logger.info(f"检测到反爬虫挑战按钮，正在点击...")
                try:
                    # 点击挑战按钮
                    await challenge_button.click()
                    # 等待内容加载
                    await page.wait_for_timeout(3000)
                    await page.wait_for_load_state('networkidle')
                    logger.info(f"成功点击挑战按钮，继续提取内容")
                except Exception as e:
                    logger.warning(f"点击挑战按钮失败: {e}")
            
            # 获取页面标题
            title = "未知标题"
            title_selectors = site_config['title_selectors']
            
            for selector in title_selectors:
                try:
                    title_element = await page.query_selector(selector)
                    if title_element:
                        title_text = await title_element.inner_text()
                        if title_text:
                            # 清理标题，移除网站名称等
                            title = self.clean_title(title_text, site_config['base_url'])
                            if title and title != "未知标题":
                                break
                except:
                    continue
            
            # 检查是否为漫画版，如果是则跳过
            if "（漫画版）" in title or "(漫画版)" in title:
                logger.info(f"跳过漫画版小说: {title}")
                return None
            
            # 检查是否为多章节小说
            chapters_element = await page.query_selector('div.chapters')
            if chapters_element:
                logger.info(f"检测到多章节小说: {title}")
                return await self.extract_multi_chapter_content(page, url, title, site_config)
            
            # 单章节小说处理
            content = ""
            try:
                # 首先尝试精确的选择器
                article_element = await page.query_selector('div.article.fiction')
                if article_element:
                    content = await article_element.inner_text()
                    logger.info(f"使用 div.article.fiction 选择器提取到内容，长度: {len(content)}")
                else:
                    # 如果精确选择器没找到，尝试其他可能的选择器
                    fallback_selectors = [
                        '.article.fiction',
                        'div.article',
                        '.fiction',
                        '.content',
                        '.novel-content',
                        'article'
                    ]
                    
                    for selector in fallback_selectors:
                        try:
                            element = await page.query_selector(selector)
                            if element:
                                temp_content = await element.inner_text()
                                if temp_content and len(temp_content.strip()) > 100:
                                    content = temp_content
                                    logger.info(f"使用备用选择器 '{selector}' 提取到内容，长度: {len(content)}")
                                    break
                        except:
                            continue
            except Exception as e:
                logger.warning(f"提取正文内容失败: {e}")
            
            # 如果还是没有内容，尝试获取页面主体文本
            if not content:
                try:
                    # 尝试获取页面中所有段落文本
                    paragraphs = await page.query_selector_all('p')
                    if paragraphs:
                        content_parts = []
                        for p in paragraphs:
                            text = await p.inner_text()
                            if text and len(text.strip()) > 20:
                                content_parts.append(text.strip())
                        if content_parts:
                            content = '\n\n'.join(content_parts)
                            logger.info(f"使用段落文本提取到内容，长度: {len(content)}")
                except Exception as e:
                    logger.warning(f"备用内容提取失败: {e}")
            
            # 清理内容
            if content:
                # 移除多余的空白字符
                content = re.sub(r'\n\s*\n', '\n\n', content)
                content = re.sub(r'\s+', ' ', content)
                content = content.strip()
            
            logger.info(f"提取完成 - 标题: {title[:50]}..., 内容长度: {len(content)}")
            
            return {
                'title': title,
                'content': content,
                'url': url,
                'source': site_config['base_url'],
                'length': len(content)
            }
            
        except Exception as e:
            logger.error(f"提取小说内容失败 {url}: {e}")
            return None
    
    async def extract_multi_chapter_content(self, page, base_url, title, site_config):
        """提取多章节小说内容"""
        try:
            logger.info(f"开始提取多章节小说: {title}")
            
            # 获取所有章节链接
            chapter_links = []
            chapters_div = await page.query_selector('div.chapters')
            if chapters_div:
                # 查找章节链接
                links = await chapters_div.query_selector_all('a[href]')
                for link in links:
                    href = await link.get_attribute('href')
                    if href:
                        # 构建完整URL
                        if href.startswith('/'):
                            chapter_url = urljoin(site_config['base_url'], href)
                        else:
                            chapter_url = href
                        chapter_links.append(chapter_url)
            
            if not chapter_links:
                logger.warning(f"未找到章节链接，回退到单章节处理")
                return None
            
            # 限制章节数量，避免过多请求
            max_chapters = 50
            if len(chapter_links) > max_chapters:
                logger.info(f"章节数量过多({len(chapter_links)})，限制为前{max_chapters}章")
                chapter_links = chapter_links[:max_chapters]
            
            logger.info(f"找到 {len(chapter_links)} 个章节")
            
            # 提取每个章节的内容
            all_content = []
            for i, chapter_url in enumerate(chapter_links, 1):
                try:
                    logger.info(f"正在提取第 {i}/{len(chapter_links)} 章: {chapter_url}")
                    chapter_content = await self.extract_chapter_content(page, chapter_url, site_config)
                    if chapter_content:
                        all_content.append(chapter_content)
                    
                    # 章节间延迟
                    await self.random_delay(1, 3)
                    
                except Exception as e:
                    logger.warning(f"提取章节 {i} 失败: {e}")
                    continue
            
            if not all_content:
                logger.warning(f"所有章节提取失败")
                return None
            
            # 合并所有章节内容
            combined_content = '\n\n'.join(all_content)
            
            logger.info(f"多章节提取完成 - 标题: {title}, 章节数: {len(all_content)}, 总长度: {len(combined_content)}")
            
            return {
                'title': title,
                'content': combined_content,
                'url': base_url,
                'source': site_config['base_url'],
                'length': len(combined_content),
                'chapters': len(all_content)
            }
            
        except Exception as e:
            logger.error(f"提取多章节小说失败 {base_url}: {e}")
            return None
    
    async def extract_chapter_content(self, page, chapter_url, site_config):
        """提取单个章节的内容"""
        try:
            await page.goto(chapter_url, wait_until='networkidle', timeout=60000)
            await self.random_delay(2, 4)
            
            # 等待页面加载完成
            await page.wait_for_load_state('domcontentloaded')
            await page.wait_for_load_state('networkidle')
            
            # 检测并等待Cloudflare验证
            await self.wait_for_cloudflare_verification(page)
            
            # 检查是否有反爬虫挑战按钮
            challenge_button = await page.query_selector('a[name="challenge"]')
            if challenge_button:
                logger.info(f"检测到反爬虫挑战按钮，正在点击...")
                try:
                    # 点击挑战按钮
                    await challenge_button.click()
                    # 等待内容加载
                    await page.wait_for_timeout(3000)
                    await page.wait_for_load_state('networkidle')
                    logger.info(f"成功点击挑战按钮，继续提取内容")
                except Exception as e:
                    logger.warning(f"点击挑战按钮失败: {e}")
            
            # 使用配置中的内容选择器提取章节内容
            content = ""
            for selector in site_config['content_selectors']:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        content = await element.inner_text()
                        if content and content.strip():
                            break
                except Exception as e:
                    logger.debug(f"选择器 {selector} 失败: {e}")
                    continue
            
            if content:
                # 清理内容
                content = re.sub(r'\n\s*\n', '\n\n', content)
                content = re.sub(r'\s+', ' ', content)
                content = content.strip()
                
                return content
            
            return None
            
        except Exception as e:
            logger.warning(f"提取章节内容失败 {chapter_url}: {e}")
            return None
    
    def classify_content(self, title, content):
        """改进的内容分类逻辑"""
        nsfw_keywords = [
            '成人', '性', '裸体', '做爱', '激情', '色情', '淫', '欲', 
            '乳房', '阴茎', '阴道', '高潮', '性交', '调教', '强奸',
            '偷情', '出轨', '乱伦', '群交', '性虐', '露出', '人妻',
            '女友', '熟妇', '美妇', '少妇', '妻子', '老婆', '鸡巴',
            '胸部', '大腿', '身体', '亲吻', '抚摸', '脱衣', '床上',
            '交媾', '姿势', '呻吟', '快感', '刺激', '兴奋', '肏',
            '编辑推荐', '长篇连载', '家庭乱伦', '多人群交', '强暴性虐',
            '校花', '美宜', '偷窥', '王聪儿', '乳记', '伴侣交换',
            '公司职场', '动漫游戏', '古典玄幻', '同性主题', '名人明星',
            '学生校园', '绿帽主题', '西方主题', '都市生活', '露出暴露'
        ]
        
        safe_keywords = [
            '学习', '工作', '友谊', '健康', '科技', '环保', '旅行',
            '美食', '读书', '教育', '新闻', '体育', '音乐', '艺术',
            '春天', '故事', '力量', '创新', '意识', '方法', '生活',
            '发展', '见闻', '文化', '心得'
        ]
        
        # 检查标题和内容前1000字符
        text_to_check = (title + ' ' + content[:1000]).lower()
        
        # 计算关键词出现次数
        safe_count = sum(1 for keyword in safe_keywords if keyword in text_to_check)
        nsfw_count = sum(1 for keyword in nsfw_keywords if keyword in text_to_check)
        
        # 由于这是成人小说网站，大部分内容都是NSFW，所以默认为NSFW
        # 只有明确包含安全关键词且不包含NSFW关键词时才标记为SAFE
        if safe_count > 0 and nsfw_count == 0:
            return 'SAFE'
        else:
            return 'NSFW'
    
    def save_novel(self, novel_data):
        """保存小说到文件"""
        if not novel_data or not novel_data['content'] or len(novel_data['content']) < 100:
            logger.warning(f"跳过内容过短的小说: {novel_data['title'] if novel_data else 'Unknown'}")
            return False
        
        try:
            # 分类内容
            category = self.classify_content(novel_data['title'], novel_data['content'])
            
            # 生成文件名
            clean_title = self.clean_filename(novel_data['title'])
            filename = f"{category}_{clean_title}.txt"
            filepath = self.output_dir / filename
            
            # 确保文件名唯一
            counter = 1
            while filepath.exists():
                filename = f"{category}_{clean_title}_{counter}.txt"
                filepath = self.output_dir / filename
                counter += 1
            
            # 保存文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"标题: {novel_data['title']}\n")
                f.write(f"来源: {novel_data['url']}\n")
                f.write(f"网站: {novel_data.get('source', 'unknown')}\n")
                f.write(f"分类: {category}\n")
                f.write("=" * 50 + "\n\n")
                f.write(novel_data['content'])
            
            # 添加到元数据
            self.metadata.append({
                'filename': filename,
                'title': novel_data['title'],
                'url': novel_data['url'],
                'source': novel_data.get('source', 'unknown'),
                'category': category,
                'length': novel_data['length'],
                'scraped_at': datetime.now().isoformat()
            })
            
            logger.info(f"保存成功: {filename} ({category})")
            return True
            
        except Exception as e:
            logger.error(f"保存小说失败: {e}")
            return False
    
    def save_metadata(self, sites_scraped=None):
        """保存元数据"""
        try:
            metadata_file = self.output_dir / 'scraped_metadata.json'
            
            # 统计各网站的小说数量
            sources = {}
            for novel in self.metadata:
                source = novel.get('source', 'unknown')
                sources[source] = sources.get(source, 0) + 1
            
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'total_novels': len(self.metadata),
                    'scraped_at': datetime.now().isoformat(),
                    'sources': sources,
                    'sites_scraped': sites_scraped or [],
                    'novels': self.metadata
                }, f, ensure_ascii=False, indent=2)
            logger.info(f"元数据已保存到: {metadata_file}")
        except Exception as e:
            logger.error(f"保存元数据失败: {e}")
    
    async def run(self, sites=['xchina', 'yazhouse8'], max_novels_per_site=20):
        """运行爬虫，支持多个网站"""
        logger.info("开始运行高级小说爬虫...")
        
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(
                headless=False,  # 设置为False可以看到浏览器操作
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            try:
                # 创建浏览器上下文
                context = await self.setup_browser_context(browser)
                page = await context.new_page()
                
                # 设置额外的反检测措施
                await page.add_init_script("""
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });
                """)
                
                all_novel_links = []
                
                # 遍历每个网站
                for site_name in sites:
                    if site_name not in self.sites_config:
                        logger.warning(f"未知网站配置: {site_name}，跳过")
                        continue
                    
                    logger.info(f"开始爬取网站: {site_name} ({self.sites_config[site_name]['base_url']})")
                    
                    try:
                        # 获取当前网站的小说链接
                        site_novel_links = await self.get_novel_links(page, site_name, max_novels_per_site)
                        
                        if site_novel_links:
                            logger.info(f"从 {site_name} 获取到 {len(site_novel_links)} 个小说链接")
                            # 为每个链接添加网站信息
                            for link in site_novel_links:
                                all_novel_links.append((link, site_name))
                        else:
                            logger.warning(f"从 {site_name} 未获取到任何小说链接")
                    
                    except Exception as e:
                        logger.error(f"爬取网站 {site_name} 时出错: {e}")
                        continue
                
                if not all_novel_links:
                    logger.error("未找到任何小说链接")
                    return
                
                logger.info(f"总共找到 {len(all_novel_links)} 个小说链接，开始提取内容...")
                
                # 爬取每个小说
                success_count = 0
                for i, (url, site_name) in enumerate(all_novel_links, 1):
                    logger.info(f"进度: {i}/{len(all_novel_links)} - {url} (来源: {site_name})")
                    
                    try:
                        site_config = self.sites_config[site_name]
                        novel_data = await self.extract_novel_content(page, url, site_config)
                        if novel_data and self.save_novel(novel_data):
                            success_count += 1
                        
                        # 随机延迟，避免被封
                        await self.random_delay(2, 5)
                        
                    except Exception as e:
                        logger.error(f"处理小说失败 {url}: {e}")
                        continue
                
                # 保存元数据
                self.save_metadata(sites)
                
                logger.info(f"爬取完成！成功爬取 {success_count} 个小说")
                
                # 统计信息
                nsfw_count = sum(1 for item in self.metadata if item['category'] == 'NSFW')
                safe_count = sum(1 for item in self.metadata if item['category'] == 'SAFE')
                
                logger.info(f"统计信息:")
                logger.info(f"- 总计: {len(self.metadata)} 个小说")
                logger.info(f"- NSFW: {nsfw_count} 个")
                logger.info(f"- SAFE: {safe_count} 个")
                
            finally:
                await browser.close()

def main():
    """主函数"""
    scraper = AdvancedNovelScraper()
    
    try:
        # 支持爬取多个网站，每个网站最多30个小说
        asyncio.run(scraper.run(sites=['xchina', 'yazhouse8'], max_novels_per_site=15))
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
    except Exception as e:
        logger.error(f"爬取过程中发生错误: {e}")

if __name__ == "__main__":
    main()