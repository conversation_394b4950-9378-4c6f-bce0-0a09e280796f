#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容安全分类器示例
使用生成的数据集训练一个简单的NSFW内容检测模型
"""

import os
import json
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from sklearn.naive_bayes import MultinomialNB
from sklearn.metrics import classification_report, confusion_matrix
import joblib
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ContentClassifier:
    def __init__(self, dataset_dir="dataset"):
        self.dataset_dir = dataset_dir
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words=None,  # 中文没有内置停用词
            ngram_range=(1, 2),
            min_df=1,
            max_df=0.95
        )
        self.classifier = MultinomialNB()
        self.model_file = "nsfw_classifier.pkl"
        self.vectorizer_file = "tfidf_vectorizer.pkl"
    
    def load_dataset(self):
        """加载数据集"""
        texts = []
        labels = []
        
        if not os.path.exists(self.dataset_dir):
            logger.error(f"数据集目录不存在: {self.dataset_dir}")
            return [], []
        
        for filename in os.listdir(self.dataset_dir):
            if filename.endswith('.txt'):
                filepath = os.path.join(self.dataset_dir, filename)
                
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 提取标签和内容
                    lines = content.split('\n')
                    label = None
                    text_content = ""
                    
                    for i, line in enumerate(lines):
                        if line.startswith('标签:'):
                            label = line.split(':', 1)[1].strip()
                        elif '=' in line and len(line) > 10:  # 分隔线
                            # 分隔线后面的内容是正文
                            text_content = '\n'.join(lines[i+1:]).strip()
                            break
                    
                    if label and text_content:
                        texts.append(text_content)
                        labels.append(label)
                        logger.debug(f"加载文件: {filename}, 标签: {label}")
                    
                except Exception as e:
                    logger.error(f"读取文件失败 {filename}: {e}")
        
        logger.info(f"成功加载 {len(texts)} 个样本")
        logger.info(f"标签分布: {dict(zip(*zip(*[(l, labels.count(l)) for l in set(labels)])))}") 
        
        return texts, labels
    
    def preprocess_text(self, text):
        """文本预处理"""
        # 移除特殊字符，保留中文、英文、数字
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', text)
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def train_model(self):
        """训练模型"""
        logger.info("开始训练模型...")
        
        # 加载数据
        texts, labels = self.load_dataset()
        
        if len(texts) == 0:
            logger.error("没有可用的训练数据")
            return False
        
        # 预处理文本
        processed_texts = [self.preprocess_text(text) for text in texts]
        
        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            processed_texts, labels, test_size=0.2, random_state=42, stratify=labels
        )
        
        logger.info(f"训练集大小: {len(X_train)}, 测试集大小: {len(X_test)}")
        
        # 特征提取
        X_train_tfidf = self.vectorizer.fit_transform(X_train)
        X_test_tfidf = self.vectorizer.transform(X_test)
        
        # 训练分类器
        self.classifier.fit(X_train_tfidf, y_train)
        
        # 预测和评估
        y_pred = self.classifier.predict(X_test_tfidf)
        
        logger.info("模型评估结果:")
        logger.info(f"\n{classification_report(y_test, y_pred)}")
        logger.info(f"\n混淆矩阵:\n{confusion_matrix(y_test, y_pred)}")
        
        # 保存模型
        try:
            joblib.dump(self.classifier, self.model_file)
            joblib.dump(self.vectorizer, self.vectorizer_file)
            logger.info(f"模型保存成功: {self.model_file}, {self.vectorizer_file}")
        except Exception as e:
            logger.error(f"模型保存失败: {e}")
            return False
        
        return True
    
    def load_model(self):
        """加载已训练的模型"""
        try:
            self.classifier = joblib.load(self.model_file)
            self.vectorizer = joblib.load(self.vectorizer_file)
            logger.info("模型加载成功")
            return True
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False
    
    def predict(self, text):
        """预测单个文本"""
        try:
            # 预处理
            processed_text = self.preprocess_text(text)
            
            # 特征提取
            text_tfidf = self.vectorizer.transform([processed_text])
            
            # 预测
            prediction = self.classifier.predict(text_tfidf)[0]
            probability = self.classifier.predict_proba(text_tfidf)[0]
            
            # 获取类别和概率
            classes = self.classifier.classes_
            prob_dict = dict(zip(classes, probability))
            
            return {
                'prediction': prediction,
                'probabilities': prob_dict,
                'confidence': max(probability)
            }
        
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return None
    
    def test_samples(self):
        """测试一些样本"""
        test_texts = [
            "今天天气很好，我和朋友一起去公园散步。",
            "她的身体很美，让人忍不住想要触摸。",
            "我正在学习机器学习，希望能够掌握这门技术。",
            "两人在床上激烈地拥抱着。",
            "春天来了，花儿都开了，景色很美。"
        ]
        
        logger.info("测试样本预测结果:")
        for i, text in enumerate(test_texts, 1):
            result = self.predict(text)
            if result:
                logger.info(f"样本 {i}: {text[:30]}...")
                logger.info(f"预测: {result['prediction']}, 置信度: {result['confidence']:.3f}")
                logger.info(f"概率分布: {result['probabilities']}")
                logger.info("-" * 50)

def main():
    classifier = ContentClassifier()
    
    # 训练模型
    if classifier.train_model():
        logger.info("模型训练完成")
        
        # 测试样本
        classifier.test_samples()
    else:
        logger.error("模型训练失败")

if __name__ == "__main__":
    main()