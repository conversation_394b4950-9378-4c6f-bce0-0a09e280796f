#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清洗脚本 - 优化版本
功能：
1. 去除网站水印和无关内容
2. 提取小说标题和正文
3. 文章去重（基于内容相似度）
4. 规范文件名格式：NSFW_文章名_章节名_分类.txt
5. 统一保存到dataset文件夹
"""

import os
import re
import logging
import hashlib
from pathlib import Path
from typing import List, Tuple, Dict, Set
import json
from datetime import datetime
from difflib import SequenceMatcher

class DataCleaner:
    def __init__(self, output_dir: str = "dataset"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

        # 强力水印清除模式
        self.watermark_keywords = [
            '小黄书', 'xchina', '看精彩成人小说', '来源:', '分类:',
            'https://', 'http://', 'www.', '.com', '.blog', '.co',
            '精彩成人小说', '成人小说', '看精彩', '上《', '》：'
        ]

        # 需要完全删除的行模式
        self.delete_line_patterns = [
            r'^来源:.*$',
            r'^分类:.*$',
            r'^https?://.*$',
            r'^www\..*$',
            r'.*xchina.*',
            r'.*小黄书.*',
            r'.*看精彩成人小说.*',
            r'^={3,}$',
            r'^-{3,}$',
            r'^\s*$'  # 空行
        ]

        # 章节标识
        self.chapter_patterns = [
            r'第[一二三四五六七八九十\d]+章',
            r'第[\d]+章',
            r'章节[\d]+',
            r'正文\s*第[\d]+章'
        ]

        # 分类关键词映射
        self.category_keywords = {
            '都市激情': ['都市', '激情', '白领', '职场', '现代'],
            '人妻交换': ['人妻', '交换', '夫妻', '已婚'],
            '校园春色': ['校园', '学生', '大学', '高中', '青春'],
            '家庭乱伦': ['家庭', '乱伦', '母子', '父女', '兄妹'],
            '制服丝袜': ['制服', '丝袜', '空姐', '护士', '秘书'],
            '同性恋情': ['同性', '男男', '女女', 'GL', 'BL'],
            'SM调教': ['SM', '调教', '束缚', '奴隶'],
            '其他': []  # 默认分类
        }

        # 去重相关
        self.content_hashes: Dict[str, str] = {}  # 内容hash -> 文件路径
        self.duplicate_files: Set[str] = set()  # 重复文件列表
        self.similarity_threshold = 0.85  # 相似度阈值

        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'skipped_files': 0,
            'duplicate_files': 0,
            'cleaned_files': []
        }

    def is_watermark_line(self, line: str) -> bool:
        """检查是否为水印行"""
        line_lower = line.lower().strip()

        # 检查关键词
        for keyword in self.watermark_keywords:
            if keyword.lower() in line_lower:
                return True

        # 检查正则模式
        for pattern in self.delete_line_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                return True

        return False

    def clean_line(self, line: str) -> str:
        """清理单行内容"""
        # 移除行内水印
        cleaned = line

        # 移除括号内的水印
        cleaned = re.sub(r'（[^）]*小黄书[^）]*）', '', cleaned)
        cleaned = re.sub(r'\([^\)]*小黄书[^\)]*\)', '', cleaned)
        cleaned = re.sub(r'（[^）]*xchina[^）]*）', '', cleaned)
        cleaned = re.sub(r'\([^\)]*xchina[^\)]*\)', '', cleaned)

        # 移除URL
        cleaned = re.sub(r'https?://[^\s）)]*', '', cleaned)

        # 移除其他水印关键词
        for keyword in self.watermark_keywords:
            cleaned = re.sub(re.escape(keyword), '', cleaned, flags=re.IGNORECASE)

        # 清理多余空格和标点
        cleaned = re.sub(r'\s+', ' ', cleaned)
        cleaned = re.sub(r'[：:]+\s*$', '', cleaned)
        cleaned = cleaned.strip()

        return cleaned

    def extract_title_and_chapter(self, lines: List[str]) -> Tuple[str, str]:
        """从内容中提取标题和章节信息"""
        title = "未知标题"
        chapter = ""

        for line in lines[:15]:  # 检查前15行
            line = line.strip()
            if not line:
                continue

            # 检查章节信息
            for pattern in self.chapter_patterns:
                match = re.search(pattern, line)
                if match and not chapter:
                    chapter = match.group(0)
                    continue

            # 提取标题（排除章节行）
            if (len(line) > 2 and len(line) < 80 and
                not any(re.search(pattern, line) for pattern in self.chapter_patterns) and
                not re.search(r'[。！？]$', line) and
                '作者' not in line and '来源' not in line):
                if title == "未知标题":
                    title = line

        return title, chapter

    def detect_category(self, content: str, title: str) -> str:
        """根据内容和标题检测分类"""
        text = (title + " " + content[:1000]).lower()  # 只检查前1000字符

        category_scores = {}
        for category, keywords in self.category_keywords.items():
            if category == '其他':
                continue
            score = sum(1 for keyword in keywords if keyword in text)
            if score > 0:
                category_scores[category] = score

        if category_scores:
            return max(category_scores, key=category_scores.get)
        return '其他'

    def calculate_content_hash(self, content: str) -> str:
        """计算内容的哈希值用于去重"""
        # 移除空白字符后计算hash
        normalized_content = re.sub(r'\s+', '', content)
        return hashlib.md5(normalized_content.encode('utf-8')).hexdigest()

    def calculate_similarity(self, content1: str, content2: str) -> float:
        """计算两个内容的相似度"""
        # 标准化内容
        norm1 = re.sub(r'\s+', '', content1)[:2000]  # 只比较前2000字符
        norm2 = re.sub(r'\s+', '', content2)[:2000]

        return SequenceMatcher(None, norm1, norm2).ratio()

    def is_duplicate_content(self, content: str, title: str) -> bool:
        """检查是否为重复内容"""
        content_hash = self.calculate_content_hash(content)

        # 检查完全相同的内容
        if content_hash in self.content_hashes:
            self.logger.info(f"发现完全重复内容: {title}")
            return True

        # 检查相似内容
        for existing_hash, existing_file in self.content_hashes.items():
            try:
                with open(existing_file, 'r', encoding='utf-8', errors='ignore') as f:
                    existing_content = f.read()

                similarity = self.calculate_similarity(content, existing_content)
                if similarity >= self.similarity_threshold:
                    self.logger.info(f"发现相似内容 (相似度: {similarity:.2f}): {title}")
                    return True
            except:
                continue

        # 记录新内容
        self.content_hashes[content_hash] = ""
        return False

    def clean_content(self, content: str) -> Tuple[str, str, str, str]:
        """彻底清洗内容，返回标题、章节、分类和内容"""
        if not content or len(content.strip()) < 50:
            return "", "", "", ""

        lines = content.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()

            # 跳过空行
            if not line:
                continue

            # 检查是否为水印行
            if self.is_watermark_line(line):
                continue

            # 清理行内容
            cleaned_line = self.clean_line(line)

            # 如果清理后还有内容，则保留
            if cleaned_line and len(cleaned_line) > 1:
                cleaned_lines.append(cleaned_line)

        if not cleaned_lines:
            return "", "", "", ""

        # 提取标题和章节
        title, chapter = self.extract_title_and_chapter(cleaned_lines)

        # 组合内容
        content_text = '\n'.join(cleaned_lines)

        # 最终清理
        content_text = re.sub(r'\n{3,}', '\n\n', content_text)
        content_text = content_text.strip()

        # 检测分类
        category = self.detect_category(content_text, title)

        return title, chapter, category, content_text

    def generate_standardized_filename(self, title: str, chapter: str, category: str, original_filename: str) -> str:
        """生成标准化文件名：NSFW_文章名_章节名_分类.txt"""
        # 清理标题
        clean_title = re.sub(r'[^\w\u4e00-\u9fff\s-]', '', title)
        clean_title = re.sub(r'\s+', '_', clean_title.strip())

        if len(clean_title) > 25:
            clean_title = clean_title[:25]

        if not clean_title or clean_title == "未知标题":
            # 使用原文件名
            base_name = Path(original_filename).stem
            clean_title = re.sub(r'[^\w\u4e00-\u9fff\s-]', '', base_name)
            clean_title = re.sub(r'\s+', '_', clean_title.strip())
            if len(clean_title) > 25:
                clean_title = clean_title[:25]

        # 清理章节名
        clean_chapter = ""
        if chapter:
            clean_chapter = re.sub(r'[^\w\u4e00-\u9fff\s-]', '', chapter)
            clean_chapter = re.sub(r'\s+', '_', clean_chapter.strip())
            if len(clean_chapter) > 15:
                clean_chapter = clean_chapter[:15]

        # 构建文件名
        filename_parts = ["NSFW", clean_title]
        if clean_chapter:
            filename_parts.append(clean_chapter)
        filename_parts.append(category)

        return "_".join(filename_parts) + ".txt"

    def process_file(self, file_path: Path) -> bool:
        """处理单个文件"""
        try:
            self.logger.info(f"处理文件: {file_path}")

            # 读取文件
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # 清洗内容
            title, chapter, category, cleaned_content = self.clean_content(content)

            if not cleaned_content or len(cleaned_content) < 100:
                self.logger.warning(f"文件内容太少或清洗失败: {file_path}")
                self.stats['skipped_files'] += 1
                return False

            # 检查是否为重复内容
            if self.is_duplicate_content(cleaned_content, title):
                self.logger.info(f"跳过重复内容: {title}")
                self.stats['duplicate_files'] += 1
                self.duplicate_files.add(str(file_path))
                return False

            # 生成标准化文件名
            output_filename = self.generate_standardized_filename(title, chapter, category, file_path.name)
            output_path = self.output_dir / output_filename

            # 避免重复文件名
            counter = 1
            while output_path.exists():
                name_parts = output_filename.rsplit('.', 1)
                if len(name_parts) == 2:
                    base_name = name_parts[0]
                    extension = name_parts[1]
                    output_filename = f"{base_name}_{counter}.{extension}"
                else:
                    output_filename = f"{output_filename}_{counter}"
                output_path = self.output_dir / output_filename
                counter += 1

            # 保存清洗后的内容
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)

            # 更新内容哈希记录
            content_hash = self.calculate_content_hash(cleaned_content)
            self.content_hashes[content_hash] = str(output_path)

            self.logger.info(f"已保存: {output_path} (分类: {category})")
            self.stats['processed_files'] += 1
            self.stats['cleaned_files'].append(str(output_path))

            return True

        except Exception as e:
            self.logger.error(f"处理文件失败 {file_path}: {e}")
            self.stats['skipped_files'] += 1
            return False

    def process_directory(self, directory: Path):
        """处理目录中的所有txt文件"""
        if not directory.exists():
            self.logger.warning(f"目录不存在: {directory}")
            return

        txt_files = list(directory.glob("*.txt"))
        self.logger.info(f"在 {directory} 中找到 {len(txt_files)} 个txt文件")

        for file_path in txt_files:
            self.stats['total_files'] += 1
            if self.process_file(file_path):
                self.stats['processed_files'] += 1
            else:
                self.stats['skipped_files'] += 1

    def generate_duplicate_removal_script(self) -> str:
        """生成删除重复文件的脚本"""
        if not self.duplicate_files:
            return "# 没有发现重复文件\n"

        script_lines = ["#!/bin/bash", "# 删除重复文件脚本", ""]

        for file_path in sorted(self.duplicate_files):
            script_lines.append(f'echo "删除重复文件: {file_path}"')
            script_lines.append(f'rm "{file_path}"')
            script_lines.append("")

        script_content = "\n".join(script_lines)

        # 保存脚本
        script_path = self.output_dir / "remove_duplicates.sh"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)

        # 设置执行权限
        os.chmod(script_path, 0o755)

        self.logger.info(f"重复文件删除脚本已生成: {script_path}")
        return script_content

    def run(self):
        """运行清洗程序"""
        self.logger.info("开始数据清洗...")
        self.logger.info(f"输出目录: {self.output_dir.absolute()}")

        # 只处理dataset目录
        dataset_dir = Path("dataset")
        if dataset_dir.exists():
            self.logger.info(f"处理目录: {dataset_dir}")
            self.process_directory(dataset_dir)

        # 生成重复文件删除脚本
        self.generate_duplicate_removal_script()

        # 保存处理统计
        stats_file = self.output_dir / "cleaning_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump({
                **self.stats,
                'timestamp': datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)

        # 输出统计信息
        self.logger.info("=" * 50)
        self.logger.info("数据清洗完成")
        self.logger.info(f"处理文件总数: {self.stats['total_files']}")
        self.logger.info(f"成功清洗文件: {self.stats['processed_files']}")
        self.logger.info(f"跳过文件数: {self.stats['skipped_files']}")
        self.logger.info(f"重复文件: {self.stats['duplicate_files']}")
        self.logger.info(f"清洗后文件保存在: {self.output_dir.absolute()}")
        self.logger.info("=" * 50)

def main():
    cleaner = DataCleaner()
    cleaner.run()

if __name__ == "__main__":
    main()