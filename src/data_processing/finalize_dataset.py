#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终整理数据集脚本
将清理后的文件移动回dataset文件夹，完成数据集整理
"""

import os
import shutil
import logging
from pathlib import Path
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('finalize_dataset.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def finalize_dataset():
    """最终整理数据集"""
    logger = setup_logging()
    
    dataset_dir = Path("dataset")
    cleaned_dir = Path("dataset_cleaned")
    backup_dir = Path("dataset_backup")
    
    if not cleaned_dir.exists():
        logger.error(f"清理后的目录不存在: {cleaned_dir}")
        return
    
    logger.info("开始最终整理数据集...")
    
    # 1. 备份原始dataset目录
    if dataset_dir.exists():
        logger.info(f"备份原始dataset目录到 {backup_dir}")
        if backup_dir.exists():
            shutil.rmtree(backup_dir)
        shutil.copytree(dataset_dir, backup_dir)
        
        # 清空原始dataset目录
        logger.info("清空原始dataset目录")
        shutil.rmtree(dataset_dir)
        dataset_dir.mkdir()
    
    # 2. 将清理后的文件移动到dataset目录
    logger.info(f"将清理后的文件从 {cleaned_dir} 移动到 {dataset_dir}")
    
    moved_count = 0
    for file_path in cleaned_dir.glob("*.txt"):
        target_path = dataset_dir / file_path.name
        shutil.move(str(file_path), str(target_path))
        moved_count += 1
        if moved_count % 50 == 0:
            logger.info(f"已移动 {moved_count} 个文件")
    
    logger.info(f"总共移动了 {moved_count} 个文件")
    
    # 3. 删除空的cleaned目录
    if cleaned_dir.exists() and not any(cleaned_dir.iterdir()):
        logger.info(f"删除空的 {cleaned_dir} 目录")
        cleaned_dir.rmdir()
    
    # 4. 生成最终统计报告
    final_count = len(list(dataset_dir.glob("*.txt")))
    
    logger.info("=" * 60)
    logger.info("数据集整理完成！")
    logger.info(f"最终dataset目录中的文件数: {final_count}")
    logger.info(f"原始文件备份在: {backup_dir}")
    logger.info(f"去重率: {((1409 - final_count) / 1409 * 100):.1f}%")
    logger.info("=" * 60)
    
    # 5. 生成README文件
    readme_content = f"""# NSFW数据集

## 数据集信息
- 整理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 文件总数: {final_count}
- 原始文件数: 1409
- 去重文件数: {1409 - final_count}
- 去重率: {((1409 - final_count) / 1409 * 100):.1f}%

## 数据清洗过程
1. 基于文件内容MD5哈希进行完全重复文件去重
2. 基于内容相似度(阈值90%)进行相似文件去重
3. 保留文件名最简洁的版本
4. 移除元数据行，只保留主要内容

## 文件格式
- 所有文件均为UTF-8编码的txt文件
- 文件名格式: NSFW_标题_[内容描述]_[分类].txt
- 内容已去除重复和相似文本

## 使用说明
此数据集用于训练NSFW内容检测模型，请确保合规使用。

## 备份信息
- 原始文件备份位置: dataset_backup/
- 去重日志: deduplication.log
- 整理日志: finalize_dataset.log
"""
    
    readme_path = dataset_dir / "README.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    logger.info(f"生成README文件: {readme_path}")

if __name__ == "__main__":
    finalize_dataset()
