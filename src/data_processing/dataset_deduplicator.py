#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集去重脚本
专门用于清理dataset文件夹中的重复txt文件
功能：
1. 基于文件内容进行去重（MD5哈希）
2. 基于内容相似度进行去重
3. 保留最好的文件版本（文件名最简洁的）
4. 生成去重报告
"""

import os
import re
import hashlib
import logging
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict
import json
from datetime import datetime
from difflib import SequenceMatcher

class DatasetDeduplicator:
    def __init__(self, dataset_dir: str = "dataset"):
        self.dataset_dir = Path(dataset_dir)
        self.cleaned_dir = Path("dataset_cleaned")
        self.cleaned_dir.mkdir(exist_ok=True)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('deduplication.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 去重数据结构
        self.content_hashes: Dict[str, List[str]] = defaultdict(list)  # hash -> [文件路径列表]
        self.similar_groups: List[List[str]] = []  # 相似文件组
        self.similarity_threshold = 0.90  # 相似度阈值
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'unique_files': 0,
            'duplicate_files': 0,
            'similar_files': 0,
            'kept_files': 0,
            'removed_files': 0
        }
    
    def calculate_content_hash(self, content: str) -> str:
        """计算内容的MD5哈希值"""
        # 标准化内容：移除空白字符、标点符号
        normalized = re.sub(r'[\s\n\r\t]+', '', content)
        normalized = re.sub(r'[^\w\u4e00-\u9fff]', '', normalized)
        return hashlib.md5(normalized.encode('utf-8')).hexdigest()
    
    def extract_main_content(self, content: str) -> str:
        """提取主要内容，去除元数据"""
        lines = content.split('\n')
        main_lines = []
        
        for line in lines:
            line = line.strip()
            # 跳过元数据行
            if (line.startswith('标题:') or 
                line.startswith('来源:') or 
                line.startswith('网站:') or 
                line.startswith('长度:') or 
                line.startswith('提取方式:') or 
                line.startswith('爬取时间:') or
                line.startswith('分类页面:') or
                not line):
                continue
            main_lines.append(line)
        
        return '\n'.join(main_lines)
    
    def calculate_similarity(self, content1: str, content2: str) -> float:
        """计算两个内容的相似度"""
        # 只比较前3000字符
        norm1 = re.sub(r'\s+', '', content1)[:3000]
        norm2 = re.sub(r'\s+', '', content2)[:3000]
        
        if not norm1 or not norm2:
            return 0.0
        
        return SequenceMatcher(None, norm1, norm2).ratio()
    
    def get_file_priority(self, filepath: str) -> int:
        """获取文件优先级，数字越小优先级越高"""
        filename = Path(filepath).name
        
        # 优先保留文件名简洁的
        priority = len(filename)
        
        # 降低包含数字后缀的文件优先级
        if re.search(r'_\d+\.txt$', filename):
            priority += 100
        
        # 降低包含"来源"、"网站"等词的文件优先级
        if any(word in filename for word in ['来源', '网站', '长度']):
            priority += 50
        
        return priority
    
    def choose_best_file(self, file_list: List[str]) -> str:
        """从重复文件列表中选择最好的文件"""
        if len(file_list) == 1:
            return file_list[0]
        
        # 按优先级排序
        sorted_files = sorted(file_list, key=self.get_file_priority)
        return sorted_files[0]
    
    def scan_files(self):
        """扫描所有txt文件并建立哈希索引"""
        self.logger.info(f"开始扫描 {self.dataset_dir} 中的txt文件...")
        
        txt_files = list(self.dataset_dir.glob("*.txt"))
        self.stats['total_files'] = len(txt_files)
        
        self.logger.info(f"找到 {len(txt_files)} 个txt文件")
        
        for file_path in txt_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # 提取主要内容
                main_content = self.extract_main_content(content)
                
                if len(main_content.strip()) < 50:
                    self.logger.warning(f"文件内容太少，跳过: {file_path}")
                    continue
                
                # 计算哈希
                content_hash = self.calculate_content_hash(main_content)
                self.content_hashes[content_hash].append(str(file_path))
                
            except Exception as e:
                self.logger.error(f"读取文件失败 {file_path}: {e}")
    
    def find_similar_files(self):
        """查找相似但不完全相同的文件"""
        self.logger.info("查找相似文件...")
        
        unique_files = []
        for hash_val, file_list in self.content_hashes.items():
            if len(file_list) == 1:
                unique_files.append(file_list[0])
        
        # 比较唯一文件之间的相似度
        for i, file1 in enumerate(unique_files):
            if i % 50 == 0:
                self.logger.info(f"相似度检查进度: {i}/{len(unique_files)}")
            
            similar_group = [file1]
            
            try:
                with open(file1, 'r', encoding='utf-8', errors='ignore') as f:
                    content1 = self.extract_main_content(f.read())
            except:
                continue
            
            for j, file2 in enumerate(unique_files[i+1:], i+1):
                try:
                    with open(file2, 'r', encoding='utf-8', errors='ignore') as f:
                        content2 = self.extract_main_content(f.read())
                    
                    similarity = self.calculate_similarity(content1, content2)
                    if similarity >= self.similarity_threshold:
                        similar_group.append(file2)
                        self.logger.info(f"发现相似文件 (相似度: {similarity:.2f}): {Path(file1).name} <-> {Path(file2).name}")
                except:
                    continue
            
            if len(similar_group) > 1:
                self.similar_groups.append(similar_group)
                # 从unique_files中移除已经分组的文件
                for file in similar_group[1:]:
                    if file in unique_files:
                        unique_files.remove(file)
    
    def deduplicate(self):
        """执行去重操作"""
        self.logger.info("开始去重操作...")
        
        kept_files = set()
        removed_files = set()
        
        # 处理完全重复的文件
        for hash_val, file_list in self.content_hashes.items():
            if len(file_list) > 1:
                best_file = self.choose_best_file(file_list)
                kept_files.add(best_file)
                
                for file_path in file_list:
                    if file_path != best_file:
                        removed_files.add(file_path)
                        self.logger.info(f"标记删除重复文件: {Path(file_path).name}")
                
                self.stats['duplicate_files'] += len(file_list) - 1
            else:
                kept_files.add(file_list[0])
        
        # 处理相似文件组
        for similar_group in self.similar_groups:
            best_file = self.choose_best_file(similar_group)
            kept_files.add(best_file)
            
            for file_path in similar_group:
                if file_path != best_file:
                    removed_files.add(file_path)
                    self.logger.info(f"标记删除相似文件: {Path(file_path).name}")
            
            self.stats['similar_files'] += len(similar_group) - 1
        
        # 复制保留的文件到清理后的目录
        for file_path in kept_files:
            try:
                source = Path(file_path)
                target = self.cleaned_dir / source.name
                
                # 避免文件名冲突
                counter = 1
                while target.exists():
                    name_parts = source.name.rsplit('.', 1)
                    if len(name_parts) == 2:
                        target = self.cleaned_dir / f"{name_parts[0]}_{counter}.{name_parts[1]}"
                    else:
                        target = self.cleaned_dir / f"{source.name}_{counter}"
                    counter += 1
                
                # 复制文件
                with open(source, 'r', encoding='utf-8', errors='ignore') as src:
                    content = src.read()
                with open(target, 'w', encoding='utf-8') as dst:
                    dst.write(content)
                
                self.stats['kept_files'] += 1
                
            except Exception as e:
                self.logger.error(f"复制文件失败 {file_path}: {e}")
        
        self.stats['removed_files'] = len(removed_files)
        self.stats['unique_files'] = len(kept_files)
        
        # 生成删除脚本
        self.generate_removal_script(removed_files)
    
    def generate_removal_script(self, removed_files: Set[str]):
        """生成删除重复文件的脚本"""
        if not removed_files:
            self.logger.info("没有需要删除的重复文件")
            return
        
        script_lines = [
            "#!/bin/bash",
            "# 自动生成的重复文件删除脚本",
            f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"# 将删除 {len(removed_files)} 个重复文件",
            "",
            "echo '开始删除重复文件...'",
            ""
        ]
        
        for file_path in sorted(removed_files):
            script_lines.append(f'echo "删除: {Path(file_path).name}"')
            script_lines.append(f'rm "{file_path}"')
            script_lines.append("")
        
        script_lines.extend([
            f"echo '删除完成，共删除 {len(removed_files)} 个重复文件'",
            f"echo '保留的唯一文件已复制到 {self.cleaned_dir} 目录'"
        ])
        
        script_content = "\n".join(script_lines)
        
        # 保存脚本
        script_path = Path("remove_duplicates.sh")
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # 设置执行权限
        os.chmod(script_path, 0o755)
        
        self.logger.info(f"删除脚本已生成: {script_path}")
    
    def generate_report(self):
        """生成去重报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'statistics': self.stats,
            'duplicate_groups': [],
            'similar_groups': []
        }
        
        # 添加重复文件组信息
        for hash_val, file_list in self.content_hashes.items():
            if len(file_list) > 1:
                report['duplicate_groups'].append({
                    'hash': hash_val,
                    'files': [str(Path(f).name) for f in file_list],
                    'kept_file': str(Path(self.choose_best_file(file_list)).name)
                })
        
        # 添加相似文件组信息
        for similar_group in self.similar_groups:
            report['similar_groups'].append({
                'files': [str(Path(f).name) for f in similar_group],
                'kept_file': str(Path(self.choose_best_file(similar_group)).name)
            })
        
        # 保存报告
        report_path = Path("deduplication_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"去重报告已生成: {report_path}")
        
        # 输出统计信息
        self.logger.info("=" * 60)
        self.logger.info("数据集去重完成")
        self.logger.info(f"原始文件总数: {self.stats['total_files']}")
        self.logger.info(f"唯一文件数: {self.stats['unique_files']}")
        self.logger.info(f"重复文件数: {self.stats['duplicate_files']}")
        self.logger.info(f"相似文件数: {self.stats['similar_files']}")
        self.logger.info(f"保留文件数: {self.stats['kept_files']}")
        self.logger.info(f"需删除文件数: {self.stats['removed_files']}")
        self.logger.info(f"去重率: {(self.stats['removed_files']/self.stats['total_files']*100):.1f}%")
        self.logger.info(f"清理后的文件保存在: {self.cleaned_dir.absolute()}")
        self.logger.info("=" * 60)
    
    def run(self):
        """运行去重程序"""
        if not self.dataset_dir.exists():
            self.logger.error(f"数据集目录不存在: {self.dataset_dir}")
            return
        
        self.logger.info("开始数据集去重...")
        
        # 扫描文件
        self.scan_files()
        
        # 查找相似文件
        self.find_similar_files()
        
        # 执行去重
        self.deduplicate()
        
        # 生成报告
        self.generate_report()

def main():
    deduplicator = DatasetDeduplicator()
    deduplicator.run()

if __name__ == "__main__":
    main()
