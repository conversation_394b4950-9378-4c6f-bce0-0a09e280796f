#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集生成器 - 基于已知内容创建NSFW内容检测数据集
用于训练内容安全风控模型
"""

import os
import json
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatasetGenerator:
    def __init__(self):
        self.dataset_dir = "dataset"
        self.metadata_file = "dataset_metadata.json"
        
        # 创建数据集目录
        if not os.path.exists(self.dataset_dir):
            os.makedirs(self.dataset_dir)
    
    def create_sample_data(self):
        """创建示例数据集"""
        
        # 基于搜索结果的已知内容片段
        sample_contents = [
            {
                "title": "校园青春故事",
                "content": "校花美宜突然有人从后迫过来，她的手还末提起就给压倒在一位别学校男孩子的胸口，两颗乳头及下体就面贴面的黏在一起。那男孩年约十五六岁，刚好与她的年龄大约差不多，他感到情况很尴尬，想避却是没有地方可动，只得保持现状。",
                "category": "学生校园",
                "label": "NSFW"
            },
            {
                "title": "王聪儿的故事",
                "content": "王聪儿身材不胖，乳房却意外得大到无法用手完全掌控，但它虽大却不夸张，只觉得恰到好处。用手托着一掂量，真金实银，分量十足。德楞泰忽然如着了魔，疯狂地吻向那对玉峰，从锁骨吻到乳沟再到乳根，如暴风骤雨般倾泻。",
                "category": "古典玄幻",
                "label": "NSFW"
            },
            {
                "title": "公园偷窥事件",
                "content": "爱偷窥的笨女孩小瑜今天如常的在公园一棵小树上，拿着一个高价的望远镜看着二十米外的一对小情侣（约莫十六岁）办那事儿。那边的两人已是接近快要高潮，小瑜在树上也是看得兴奋，却没想到她一下失了重心，竟从树上掉了下来。",
                "category": "都市生活",
                "label": "NSFW"
            },
            {
                "title": "熟妇交往记录",
                "content": "小狼今年27岁了，身体健壮，喜欢和熟妇做，经常上UC和本地的熟妇聊天，一般都选择在27-35岁的熟妇。今年认识了一个同城的熟妇，是一个中学的老师，年龄是30岁，在UC上认识的，聊了3个月决定约她出来。",
                "category": "人妻女友",
                "label": "NSFW"
            },
            {
                "title": "美国留学经历",
                "content": "三年后，我大学毕业，经一个美国教授推荐，到美国东岸的一所名校读博士学位。来美不久，在朋友的聚会上结识了一个美国姑娘凯丽。凯丽比我大九岁，在附近一所大学的心理学系做研究生。凯丽性欲极强，每天都要做爱。",
                "category": "家庭乱伦",
                "label": "NSFW"
            },
            {
                "title": "古代宫廷故事",
                "content": "哦，烫死了，你这个坏家伙非得把祖母这老身骨给折腾坏了不可！荣国夫人同样大叫一声，玉手死死的勾住李逸飞的脑袋，垂涎欲滴的诱人鲜唇立刻迎了上来。李逸飞来者不拒，厚唇与美妇人的鲜唇撕咬在一起，灵舌不时的。",
                "category": "古典玄幻",
                "label": "NSFW"
            },
            {
                "title": "调教题材",
                "content": "哼…月柔哀叫一声，这么羞耻的样子，又怎么凝聚尿意呢？用以前教你的方法不会吗？那边不是有工具吗？男子命令的口吻说。月柔闻言默默往前爬，来到伸手可及床头柜的距离，拾起了一根可能是发夹的小东西。",
                "category": "强暴性虐",
                "label": "NSFW"
            },
            {
                "title": "历史题材",
                "content": "别急，急什么？剥衣服的清兵故意吊着大家的胃口。他们解开洪宣娇的裤带，抓住她的裤腰轻轻向下一拉，露出年轻女人深深的脐孔和圆滑的两髋，女人细细的腰肢此时显得那么柔，那么美。他们先握住了女人那高耸的乳房。",
                "category": "古典玄幻",
                "label": "NSFW"
            },
            {
                "title": "家庭关系",
                "content": "当妈妈脱下毛衣露出里面的风光时才让罗文腾大吃一惊。只见身体里穿着的都不能叫衣服妈妈白花花的肉体上来来回回缠着的是一圈圈红色的情趣捆绑绳。胸罩当然是没有的妈妈的两颗白花花的大奶子被红色的绳子交叉缠绕显得。",
                "category": "家庭乱伦",
                "label": "NSFW"
            },
            {
                "title": "大学生活",
                "content": "她头上戴的那个发卡正是我高一送给她的生日礼物……廖雨涛和林一川此时同时扭头看着我，眼睛里充满了同情，而我则獃獃的看着电脑屏幕，双拳紧握！崩溃、伤心、愤怒、兴奋此时此刻已经完全占据了我的大脑！",
                "category": "都市生活",
                "label": "NSFW"
            }
        ]
        
        # 添加更多正常内容作为对比
        normal_contents = [
            {
                "title": "春天的故事",
                "content": "春天来了，万物复苏，大地一片生机勃勃。小明走在校园的小径上，看着路边盛开的樱花，心情格外舒畅。今天是他大学生活的第一天，对未来充满了期待和憧憬。阳光透过树叶洒在地面上，形成斑驳的光影。",
                "category": "校园生活",
                "label": "SAFE"
            },
            {
                "title": "友谊的力量",
                "content": "李华和王明是从小一起长大的好朋友。虽然他们性格迥异，但友谊却非常深厚。在面临人生重要选择时，他们总是互相支持，共同面对困难和挑战。真正的友谊经得起时间的考验，也经得起距离的分离。",
                "category": "友情故事",
                "label": "SAFE"
            },
            {
                "title": "科技创新",
                "content": "随着人工智能技术的快速发展，我们的生活正在发生翻天覆地的变化。从智能手机到自动驾驶汽车，科技正在让我们的生活变得更加便利和高效。机器学习算法在各个领域都有广泛应用，推动着社会进步。",
                "category": "科技文章",
                "label": "SAFE"
            },
            {
                "title": "环保意识",
                "content": "保护环境是我们每个人的责任。通过垃圾分类、节约用水、使用可再生能源等方式，我们可以为地球的可持续发展贡献自己的力量。绿色出行、减少塑料使用、植树造林都是很好的环保行为。",
                "category": "环保教育",
                "label": "SAFE"
            },            {
                "title": "学习方法",
                "content": "有效的学习方法对于学生来说至关重要。制定合理的学习计划、保持专注、定期复习和实践应用，这些都是提高学习效率的关键因素。好的学习习惯需要长期坚持才能养成。",
                "category": "教育指导",
                "label": "SAFE"
            },
            {
                "title": "健康生活",
                "content": "保持健康的生活方式对每个人都很重要。规律作息、均衡饮食、适量运动是健康生活的三大支柱。早睡早起身体好，多吃蔬菜水果，少吃油腻食物，每天坚持锻炼半小时。",
                "category": "健康养生",
                "label": "SAFE"
            },
            {
                "title": "职场发展",
                "content": "在职场中，不断学习和提升自己的能力是非常重要的。要善于沟通合作，积极主动地承担责任，保持专业的工作态度。同时也要平衡工作和生活，避免过度劳累。",
                "category": "职场励志",
                "label": "SAFE"
            },
            {
                "title": "旅行见闻",
                "content": "这次旅行让我收获很多。走过不同的城市，看过不同的风景，遇见不同的人。每个地方都有自己独特的文化和历史，让人印象深刻。旅行不仅开阔了视野，也丰富了人生阅历。",
                "category": "旅行游记",
                "label": "SAFE"
            },
            {
                "title": "美食文化",
                "content": "中华美食文化博大精深，每个地区都有自己的特色菜肴。从北方的饺子到南方的汤圆，从川菜的麻辣到粤菜的清淡，都体现了不同地域的文化特色。品尝美食也是了解文化的一种方式。",
                "category": "美食文化",
                "label": "SAFE"
            },
            {
                "title": "读书心得",
                "content": "读书是一种很好的学习方式。通过阅读，我们可以了解不同的思想观点，学习新的知识技能，提升自己的思维能力。好书如良师益友，能够指导我们的人生方向，丰富我们的精神世界。",
                "category": "读书学习",
                "label": "SAFE"
            }
        ]
        
        all_contents = sample_contents + normal_contents
        
        return all_contents
    
    def save_dataset(self, contents):
        """保存数据集"""
        metadata = {
            "created_at": datetime.now().isoformat(),
            "total_samples": len(contents),
            "nsfw_samples": len([c for c in contents if c['label'] == 'NSFW']),
            "safe_samples": len([c for c in contents if c['label'] == 'SAFE']),
            "categories": list(set([c['category'] for c in contents])),
            "description": "NSFW内容检测训练数据集"
        }
        
        success_count = 0
        
        for i, content in enumerate(contents, 1):
            filename = f"{i:03d}_{content['label']}_{content['title'][:30]}"
            # 清理文件名中的非法字符
            filename = "".join(c for c in filename if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()
            filename = filename.replace(' ', '_') + '.txt'
            
            filepath = os.path.join(self.dataset_dir, filename)
            
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(f"标题: {content['title']}\n")
                    f.write(f"分类: {content['category']}\n")
                    f.write(f"标签: {content['label']}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(content['content'])
                
                logger.info(f"保存成功: {filename}")
                success_count += 1
                
            except Exception as e:
                logger.error(f"保存文件失败 {filename}: {e}")
        
        # 保存元数据
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            logger.info(f"元数据保存成功: {self.metadata_file}")
        except Exception as e:
            logger.error(f"保存元数据失败: {e}")
        
        return success_count, metadata
    
    def generate_dataset(self):
        """生成完整数据集"""
        logger.info("开始生成数据集...")
        
        contents = self.create_sample_data()
        success_count, metadata = self.save_dataset(contents)
        
        logger.info(f"数据集生成完成！")
        logger.info(f"成功保存 {success_count} 个样本")
        logger.info(f"NSFW样本: {metadata['nsfw_samples']} 个")
        logger.info(f"安全样本: {metadata['safe_samples']} 个")
        logger.info(f"涵盖分类: {', '.join(metadata['categories'])}")
        
        return metadata

def main():
    generator = DatasetGenerator()
    generator.generate_dataset()

if __name__ == "__main__":
    main()