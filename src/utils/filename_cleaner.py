#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名清理工具

整理数据集文件名，去除重复和无意义字符，
统一格式为: NSFW_{分类}_{文章标题}_{章节}.txt

功能:
1. 去除重复的"NSFW"、"标题"等无意义字符
2. 提取和标准化分类信息
3. 清理文章标题，去除特殊字符
4. 标准化章节信息
5. 处理重复文件名冲突
"""

import os
import re
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from collections import defaultdict, Counter
import json

class FilenameCleaner:
    """文件名清理器"""
    
    def __init__(self, dataset_dir: str = "dataset"):
        self.dataset_dir = Path(dataset_dir)
        self.setup_logging()
        
        # 分类映射表
        self.category_mapping = {
            '都市激情': '都市激情',
            '家庭乱伦': '家庭乱伦', 
            '人妻交换': '人妻交换',
            '校园春色': '校园春色',
            '制服丝袜': '制服丝袜',
            '同性恋情': '同性恋情',
            '其他': '其他',
            '未分类': '其他'
        }
        
        # 无意义词汇列表
        self.meaningless_words = [
            'NSFW_', 'NSFW', '标题_', '标题', '长度_', '长度', '字符_', '字符',
            '来源_', '来源', '网站_', '网站', '分类页面_', '分类页面',
            '提取方式_', '提取方式', '爬取时间_', '爬取时间'
        ]
        
        # 章节关键词
        self.chapter_keywords = [
            '第一章', '第二章', '第三章', '第四章', '第五章', '第六章', '第七章', '第八章', '第九章', '第十章',
            '第十一章', '第十二章', '第十三章', '第十四章', '第十五章', '第十六章', '第十七章', '第十八章', '第十九章', '第二十章',
            '第二十一章', '第二十二章', '第二十三章', '第1章', '第2章', '第3章', '第4章', '第5章', '第6章', '第7章', '第8章', '第9章',
            '番外', '序章', '尾声', '完结篇'
        ]
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'renamed_files': 0,
            'skipped_files': 0,
            'error_files': 0,
            'categories': Counter(),
            'conflicts': 0
        }
        
        # 重命名映射
        self.rename_mapping = {}
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/filename_cleaning.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def extract_category(self, filename: str) -> str:
        """从文件名中提取分类"""
        filename_lower = filename.lower()
        
        # 直接匹配分类名
        for category in self.category_mapping.keys():
            if category in filename:
                return self.category_mapping[category]
        
        # 根据关键词推断分类
        if any(word in filename for word in ['迷奸', '调教', '轮奸']):
            return '都市激情'
        elif any(word in filename for word in ['妈妈', '姐姐', '妹妹', '表姐', '表妹', '舅妈', '母子', '儿媳']):
            return '家庭乱伦'
        elif any(word in filename for word in ['老婆', '人妻', '交换']):
            return '人妻交换'
        elif any(word in filename for word in ['校园', '学生', '老师', '校花']):
            return '校园春色'
        elif any(word in filename for word in ['丝袜', '制服']):
            return '制服丝袜'
        elif any(word in filename for word in ['同性', '女同', '男同']):
            return '同性恋情'
        else:
            return '其他'
    
    def extract_title(self, filename: str) -> str:
        """从文件名中提取标题"""
        # 移除文件扩展名
        name = filename.replace('.txt', '')
        
        # 移除无意义词汇
        for word in self.meaningless_words:
            name = name.replace(word, '')
        
        # 移除分类信息
        for category in self.category_mapping.keys():
            name = name.replace(category, '')
        
        # 移除章节信息
        for chapter in self.chapter_keywords:
            name = name.replace(chapter, '')
        
        # 清理特殊字符和多余的分隔符
        name = re.sub(r'[-_\s]+', '_', name)  # 统一分隔符
        name = re.sub(r'^[_\-\s]+|[_\-\s]+$', '', name)  # 去除首尾分隔符
        name = re.sub(r'_+', '_', name)  # 合并多个下划线
        
        # 移除数字后缀 (如 _1, _2, _3)
        name = re.sub(r'_\d+$', '', name)
        
        # 移除特殊字符
        name = re.sub(r'[^\w\u4e00-\u9fff_]', '', name)
        
        # 如果标题为空或太短，使用默认标题
        if not name or len(name) < 2:
            name = '未知标题'
        
        return name
    
    def extract_chapter(self, filename: str) -> Optional[str]:
        """从文件名中提取章节信息"""
        for chapter in self.chapter_keywords:
            if chapter in filename:
                return chapter
        
        # 检查数字章节格式
        chapter_match = re.search(r'第(\d+)章', filename)
        if chapter_match:
            return f'第{chapter_match.group(1)}章'
        
        # 检查简单数字后缀
        number_match = re.search(r'_(\d+)\.txt$', filename)
        if number_match and int(number_match.group(1)) > 1:
            return f'第{number_match.group(1)}章'
        
        return None
    
    def generate_new_filename(self, old_filename: str) -> str:
        """生成新的文件名"""
        # 提取各部分信息
        category = self.extract_category(old_filename)
        title = self.extract_title(old_filename)
        chapter = self.extract_chapter(old_filename)
        
        # 构建新文件名
        parts = ['NSFW', category, title]
        if chapter:
            parts.append(chapter)
        
        new_filename = '_'.join(parts) + '.txt'
        
        # 清理文件名中的特殊字符
        new_filename = re.sub(r'[<>:"/\\|?*]', '', new_filename)
        
        return new_filename
    
    def handle_filename_conflict(self, new_filename: str, existing_files: set) -> str:
        """处理文件名冲突"""
        if new_filename not in existing_files:
            return new_filename
        
        # 添加数字后缀解决冲突
        base_name = new_filename.replace('.txt', '')
        counter = 2
        
        while True:
            candidate = f"{base_name}_{counter}.txt"
            if candidate not in existing_files:
                self.stats['conflicts'] += 1
                self.logger.warning(f"文件名冲突，使用: {candidate}")
                return candidate
            counter += 1
    
    def clean_filenames(self, dry_run: bool = True) -> Dict:
        """清理所有文件名"""
        if not self.dataset_dir.exists():
            self.logger.error(f"数据集目录不存在: {self.dataset_dir}")
            return self.stats
        
        self.logger.info(f"开始清理文件名 (dry_run={dry_run})...")
        
        # 获取所有txt文件
        txt_files = list(self.dataset_dir.glob("*.txt"))
        self.stats['total_files'] = len(txt_files)
        
        if not txt_files:
            self.logger.warning("未找到任何txt文件")
            return self.stats
        
        # 用于跟踪新文件名，避免冲突
        new_filenames = set()
        
        # 处理每个文件
        for old_path in txt_files:
            try:
                old_filename = old_path.name
                new_filename = self.generate_new_filename(old_filename)
                
                # 处理文件名冲突
                new_filename = self.handle_filename_conflict(new_filename, new_filenames)
                new_filenames.add(new_filename)
                
                # 记录重命名映射
                if old_filename != new_filename:
                    self.rename_mapping[old_filename] = new_filename
                    category = self.extract_category(old_filename)
                    self.stats['categories'][category] += 1
                    
                    self.logger.info(f"重命名: {old_filename} -> {new_filename}")
                    
                    # 如果不是dry run，执行实际重命名
                    if not dry_run:
                        new_path = self.dataset_dir / new_filename
                        old_path.rename(new_path)
                    
                    self.stats['renamed_files'] += 1
                else:
                    self.stats['skipped_files'] += 1
                    
            except Exception as e:
                self.logger.error(f"处理文件失败 {old_path.name}: {e}")
                self.stats['error_files'] += 1
        
        # 保存重命名映射
        self.save_rename_mapping()
        
        return self.stats
    
    def save_rename_mapping(self):
        """保存重命名映射到文件"""
        if not self.rename_mapping:
            return
        
        mapping_file = Path("data/metadata/filename_rename_mapping.json")
        mapping_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(self.rename_mapping, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"重命名映射已保存到: {mapping_file}")
    
    def generate_report(self):
        """生成清理报告"""
        self.logger.info("=" * 60)
        self.logger.info("文件名清理报告")
        self.logger.info("=" * 60)
        self.logger.info(f"总文件数: {self.stats['total_files']}")
        self.logger.info(f"重命名文件数: {self.stats['renamed_files']}")
        self.logger.info(f"跳过文件数: {self.stats['skipped_files']}")
        self.logger.info(f"错误文件数: {self.stats['error_files']}")
        self.logger.info(f"文件名冲突数: {self.stats['conflicts']}")
        
        if self.stats['categories']:
            self.logger.info("\n分类分布:")
            for category, count in self.stats['categories'].most_common():
                self.logger.info(f"  {category}: {count} 个文件")
        
        self.logger.info("=" * 60)
    
    def preview_changes(self, limit: int = 20):
        """预览重命名变更"""
        self.logger.info(f"预览前 {limit} 个重命名变更:")
        self.logger.info("-" * 80)
        
        count = 0
        for old_name, new_name in list(self.rename_mapping.items())[:limit]:
            self.logger.info(f"{old_name}")
            self.logger.info(f"  -> {new_name}")
            self.logger.info("")
            count += 1
        
        if len(self.rename_mapping) > limit:
            self.logger.info(f"... 还有 {len(self.rename_mapping) - limit} 个文件需要重命名")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="清理数据集文件名")
    parser.add_argument("--dataset-dir", default="dataset", help="数据集目录")
    parser.add_argument("--dry-run", action="store_true", help="预览模式，不实际重命名")
    parser.add_argument("--preview", type=int, default=20, help="预览变更数量")
    
    args = parser.parse_args()
    
    cleaner = FilenameCleaner(args.dataset_dir)
    
    # 执行清理
    stats = cleaner.clean_filenames(dry_run=args.dry_run)
    
    # 预览变更
    if args.dry_run and cleaner.rename_mapping:
        cleaner.preview_changes(args.preview)
    
    # 生成报告
    cleaner.generate_report()
    
    if args.dry_run:
        print("\n这是预览模式，没有实际重命名文件。")
        print("要执行实际重命名，请运行: python filename_cleaner.py")


if __name__ == "__main__":
    main()
