# NSFW内容安全风控数据集项目

这个项目用于创建NSFW内容检测的训练数据集，并提供了一个简单的内容安全分类器示例。项目包含数据集生成、模型训练和内容分类功能。

## 功能特性

- **数据集生成**：基于已知内容创建NSFW和安全内容的训练样本
- **内容分类**：使用机器学习算法训练NSFW内容检测模型
- **模型评估**：提供分类报告和混淆矩阵评估模型性能
- **实时预测**：支持对新文本进行NSFW内容检测
- **数据平衡**：包含相等数量的NSFW和安全内容样本
- **详细日志**：完整的训练和预测过程日志记录

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 生成数据集

```bash
python dataset_generator.py
```

### 2. 训练分类模型

```bash
python content_classifier.py
```

### 3. 使用训练好的模型进行预测

模型训练完成后，会自动保存为 `nsfw_classifier.pkl` 和 `tfidf_vectorizer.pkl` 文件，可以加载后进行预测。

## 输出格式

### 数据集文件
- `dataset/` 目录包含所有训练样本文件
- 文件命名格式：`[NSFW/SAFE]_分类_标题.txt`
- `dataset_metadata.json` 包含数据集的详细元信息

### 模型文件
- `nsfw_classifier.pkl`：训练好的分类器模型
- `tfidf_vectorizer.pkl`：TF-IDF向量化器

## 数据集统计

当前数据集包含：
- **总样本数**：20个
- **NSFW样本**：10个
- **安全样本**：10个
- **涵盖分类**：教育指导、健康养生、友情故事、科技文章、职场励志、强暴性虐、校园生活、旅行游记、古典玄幻、美食文化、人妻女友、读书学习、都市生活、家庭乱伦、环保教育、学生校园

## 注意事项

- 本项目仅用于学习和研究目的
- 数据集中的NSFW内容为模拟生成，用于训练分类模型
- 实际应用中需要更大规模和更多样化的数据集
- 模型性能可能需要进一步优化和调整

## 目录结构

```
NSFW/
├── dataset_generator.py     # 数据集生成脚本
├── content_classifier.py    # 内容分类器训练脚本
├── novel_scraper.py        # 网络爬虫脚本（备用）
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明文档
├── dataset_metadata.json  # 数据集元信息
├── nsfw_classifier.pkl    # 训练好的分类器模型
├── tfidf_vectorizer.pkl   # TF-IDF向量化器
└── dataset/               # 训练数据集目录
    ├── NSFW_强暴性虐_禁忌的诱惑.txt
    ├── SAFE_教育指导_学习方法.txt
    └── ...
```

## 技术实现

- **请求库**：使用requests进行HTTP请求
- **解析库**：使用BeautifulSoup4进行HTML解析
- **编码处理**：自动处理UTF-8编码
- **错误恢复**：包含重试机制和异常处理
- **日志系统**：详细记录爬取过程和错误信息

## 免责声明

本工具仅用于学习和研究目的，用于训练内容安全风控模型。使用者需要：

1. 遵守相关法律法规
2. 尊重网站的使用条款
3. 合理控制爬取频率
4. 仅将数据用于合法的研究目的

使用本工具产生的任何法律责任由使用者自行承担。