# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# 项目特定文件
# 数据集文件
dataset/
dataset_backup/
dataset_cleaned/
yazhouse8_novels/
yazhouse8_novels_improved/
yazhouse8_novels_ultimate/

# 日志文件
logs/
*.log

# 模型文件
*.pkl
*.joblib
*.model

# 数据文件
data/models/
data/statistics/
data/metadata/

# 配置文件中的敏感信息
config/secrets.yaml
config/local_config.yaml

# 临时文件
temp/
tmp/
*.tmp
*.temp

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 脚本生成的文件
remove_duplicates.sh
deduplication_report.json
dataset_statistics.json

# 测试文件
test_output/
test_data/

# 文档生成文件
docs/_build/
docs/build/

# 性能分析文件
*.prof
*.profile

# 缓存文件
.cache/
cache/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 媒体文件
*.jpg
*.jpeg
*.png
*.gif
*.mp4
*.avi
*.mov

# 大文件
*.bin
*.dat
*.dump
