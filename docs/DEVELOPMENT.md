# 开发指南

## 开发环境设置

### 环境要求

- Python 3.8+
- Git
- 8GB+ RAM (推荐)
- 10GB+ 可用磁盘空间

### 开发环境安装

1. **克隆项目**
```bash
git clone <repository-url>
cd NSFW
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. **安装开发依赖**
```bash
pip install -r config/requirements.txt
pip install -r config/requirements-dev.txt  # 开发依赖
```

4. **安装pre-commit钩子**
```bash
pre-commit install
```

## 代码规范

### 编码标准

项目遵循以下编码标准：

- **PEP 8**: Python代码风格指南
- **Black**: 代码格式化工具
- **isort**: 导入语句排序
- **flake8**: 代码质量检查
- **mypy**: 类型检查

### 代码格式化

使用Black进行代码格式化：

```bash
# 格式化所有代码
black src/

# 格式化特定文件
black src/scrapers/novel_scraper.py

# 检查格式但不修改
black --check src/
```

### 导入语句排序

使用isort排序导入语句：

```bash
# 排序所有文件
isort src/

# 排序特定文件
isort src/scrapers/novel_scraper.py
```

### 代码质量检查

使用flake8检查代码质量：

```bash
# 检查所有代码
flake8 src/

# 检查特定文件
flake8 src/scrapers/novel_scraper.py
```

### 类型检查

使用mypy进行类型检查：

```bash
# 检查所有代码
mypy src/

# 检查特定模块
mypy src/scrapers/
```

## 项目结构

### 目录说明

```
src/
├── scrapers/           # 爬虫模块
│   ├── __init__.py
│   ├── base.py        # 基础爬虫类
│   ├── novel_scraper.py
│   └── advanced_scraper.py
├── data_processing/    # 数据处理模块
│   ├── __init__.py
│   ├── cleaner.py     # 数据清洗
│   ├── deduplicator.py # 去重处理
│   └── validator.py   # 数据验证
├── analysis/          # 分析模块
│   ├── __init__.py
│   ├── stats.py       # 统计分析
│   ├── classifier.py  # 分类器
│   └── visualizer.py  # 可视化
└── utils/             # 工具模块
    ├── __init__.py
    ├── config.py      # 配置管理
    ├── logger.py      # 日志工具
    └── file_utils.py  # 文件操作
```

### 模块设计原则

1. **单一职责**: 每个模块只负责一个特定功能
2. **松耦合**: 模块间依赖最小化
3. **高内聚**: 相关功能组织在一起
4. **可扩展**: 易于添加新功能
5. **可测试**: 便于编写单元测试

## 编写新功能

### 添加新爬虫

1. **创建爬虫类**

```python
# src/scrapers/new_scraper.py
from typing import Dict, List, Optional
from .base import BaseScraper

class NewScraper(BaseScraper):
    """新站点爬虫"""
    
    def __init__(self, base_url: str, **kwargs):
        super().__init__(base_url, **kwargs)
        self.site_name = "new_site"
    
    def parse_content(self, html: str) -> Dict[str, str]:
        """解析页面内容"""
        # 实现具体的解析逻辑
        pass
    
    def get_article_urls(self, category_url: str) -> List[str]:
        """获取文章URL列表"""
        # 实现URL提取逻辑
        pass
```

2. **更新__init__.py**

```python
# src/scrapers/__init__.py
from .new_scraper import NewScraper

__all__ = [
    "NovelScraper",
    "AdvancedScraper",
    "NewScraper"  # 添加新爬虫
]
```

3. **编写测试**

```python
# tests/test_scrapers/test_new_scraper.py
import pytest
from src.scrapers import NewScraper

class TestNewScraper:
    def test_init(self):
        scraper = NewScraper("https://example.com")
        assert scraper.base_url == "https://example.com"
    
    def test_parse_content(self):
        scraper = NewScraper("https://example.com")
        html = "<html>...</html>"
        result = scraper.parse_content(html)
        assert isinstance(result, dict)
```

### 添加新数据处理功能

1. **创建处理类**

```python
# src/data_processing/new_processor.py
from typing import List, Dict
from pathlib import Path

class NewProcessor:
    """新的数据处理器"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def process(self, data: List[Dict]) -> List[Dict]:
        """处理数据"""
        # 实现处理逻辑
        pass
```

2. **添加配置选项**

```yaml
# config/processor_config.yaml
new_processor:
  enabled: true
  option1: value1
  option2: value2
```

### 添加新分析功能

遵循类似的模式，在相应模块中添加新类和功能。

## 测试

### 测试框架

项目使用pytest作为测试框架。

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定模块测试
pytest tests/test_scrapers/

# 运行特定测试文件
pytest tests/test_scrapers/test_novel_scraper.py

# 运行特定测试方法
pytest tests/test_scrapers/test_novel_scraper.py::TestNovelScraper::test_init

# 生成覆盖率报告
pytest --cov=src tests/

# 生成HTML覆盖率报告
pytest --cov=src --cov-report=html tests/
```

### 编写测试

#### 单元测试示例

```python
import pytest
from unittest.mock import Mock, patch
from src.scrapers import NovelScraper

class TestNovelScraper:
    def setup_method(self):
        """每个测试方法前执行"""
        self.scraper = NovelScraper("https://example.com")
    
    def test_init(self):
        """测试初始化"""
        assert self.scraper.base_url == "https://example.com"
        assert self.scraper.delay_range == (1, 3)
    
    @patch('requests.get')
    def test_scrape_page_success(self, mock_get):
        """测试成功爬取页面"""
        # 模拟响应
        mock_response = Mock()
        mock_response.text = "<html><title>Test</title></html>"
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        result = self.scraper.scrape_page("https://example.com/page1")
        
        assert result is not None
        assert "title" in result
    
    def test_scrape_page_invalid_url(self):
        """测试无效URL"""
        with pytest.raises(ValueError):
            self.scraper.scrape_page("invalid-url")
```

#### 集成测试示例

```python
import tempfile
import shutil
from pathlib import Path
from src.data_processing import DataCleaner

class TestDataCleanerIntegration:
    def setup_method(self):
        """创建临时目录"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.input_dir = self.temp_dir / "input"
        self.output_dir = self.temp_dir / "output"
        self.input_dir.mkdir()
        self.output_dir.mkdir()
    
    def teardown_method(self):
        """清理临时目录"""
        shutil.rmtree(self.temp_dir)
    
    def test_clean_dataset(self):
        """测试数据集清洗"""
        # 创建测试文件
        test_file = self.input_dir / "test.txt"
        test_file.write_text("测试内容", encoding="utf-8")
        
        cleaner = DataCleaner(self.input_dir, self.output_dir)
        cleaner.clean_dataset()
        
        # 验证输出
        output_files = list(self.output_dir.glob("*.txt"))
        assert len(output_files) > 0
```

### 测试覆盖率

目标测试覆盖率：
- 整体覆盖率 > 80%
- 核心模块覆盖率 > 90%
- 关键函数覆盖率 = 100%

## 文档

### 文档字符串

使用Google风格的文档字符串：

```python
def scrape_page(self, url: str, timeout: int = 30) -> Dict[str, str]:
    """爬取单个页面内容.
    
    Args:
        url: 要爬取的页面URL
        timeout: 请求超时时间，默认30秒
    
    Returns:
        包含页面信息的字典，包含以下键：
        - title: 页面标题
        - content: 页面内容
        - url: 页面URL
        - timestamp: 爬取时间戳
    
    Raises:
        ValueError: URL格式无效
        ConnectionError: 网络连接失败
        TimeoutError: 请求超时
    
    Example:
        >>> scraper = NovelScraper("https://example.com")
        >>> result = scraper.scrape_page("https://example.com/page1")
        >>> print(result['title'])
        'Page Title'
    """
```

### API文档

使用Sphinx生成API文档：

```bash
# 安装Sphinx
pip install sphinx sphinx-rtd-theme

# 初始化文档
sphinx-quickstart docs/

# 生成文档
cd docs/
make html
```

## 版本控制

### Git工作流

使用Git Flow工作流：

1. **主分支**
   - `main`: 生产环境代码
   - `develop`: 开发环境代码

2. **功能分支**
   - `feature/功能名`: 新功能开发
   - `bugfix/问题名`: 问题修复
   - `hotfix/紧急修复`: 紧急修复

3. **分支命名规范**
   - `feature/add-new-scraper`
   - `bugfix/fix-encoding-issue`
   - `hotfix/fix-critical-bug`

### 提交信息规范

使用约定式提交格式：

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

类型：
- `feat`: 新功能
- `fix`: 问题修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(scrapers): 添加新站点爬虫支持

- 实现NewScraper类
- 添加配置选项
- 更新文档

Closes #123
```

## 性能优化

### 性能监控

使用cProfile进行性能分析：

```python
import cProfile
import pstats

# 性能分析
profiler = cProfile.Profile()
profiler.enable()

# 执行代码
your_function()

profiler.disable()

# 查看结果
stats = pstats.Stats(profiler)
stats.sort_stats('cumulative')
stats.print_stats(10)
```

### 内存优化

1. **使用生成器**代替列表
2. **及时释放**大对象
3. **使用__slots__**减少内存占用
4. **批量处理**大数据集

### 并发优化

1. **I/O密集型**：使用asyncio或threading
2. **CPU密集型**：使用multiprocessing
3. **合理设置**并发数量
4. **避免**竞态条件

## 部署

### 生产环境部署

参考 [DEPLOYMENT.md](DEPLOYMENT.md) 文档。

### Docker部署

```dockerfile
FROM python:3.8-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ src/
COPY config/ config/

CMD ["python", "-m", "src.main"]
```

## 贡献流程

1. **Fork项目**
2. **创建功能分支**
3. **编写代码和测试**
4. **运行所有测试**
5. **提交Pull Request**
6. **代码审查**
7. **合并代码**

## 常见问题

### Q: 如何添加新的配置选项？

A: 在相应的YAML配置文件中添加选项，然后在代码中读取配置。

### Q: 如何处理编码问题？

A: 统一使用UTF-8编码，在文件读写时明确指定编码格式。

### Q: 如何优化爬虫性能？

A: 合理设置并发数、请求延迟，使用连接池，缓存重复请求。

### Q: 如何调试复杂问题？

A: 使用日志记录关键信息，使用调试器逐步执行，编写单元测试验证。
