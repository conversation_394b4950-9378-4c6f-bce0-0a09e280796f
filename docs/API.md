# API 文档

## 概述

本文档描述了NSFW数据集项目的主要API接口和使用方法。

## 爬虫模块 (src.scrapers)

### NovelScraper

基础小说爬虫类，用于爬取单个网站的内容。

#### 初始化

```python
from src.scrapers import NovelScraper

scraper = NovelScraper(
    base_url="https://example.com",
    delay_range=(1, 3),
    max_retries=3
)
```

#### 参数

- `base_url` (str): 目标网站的基础URL
- `delay_range` (tuple): 请求延迟范围，格式为(min, max)
- `max_retries` (int): 最大重试次数

#### 方法

##### scrape_page(url)

爬取单个页面的内容。

```python
content = scraper.scrape_page("https://example.com/page1")
```

**参数:**
- `url` (str): 要爬取的页面URL

**返回:**
- `dict`: 包含标题、内容、URL等信息的字典

##### scrape_category(category_url, max_pages=10)

爬取分类页面下的所有内容。

```python
results = scraper.scrape_category(
    "https://example.com/category/fiction",
    max_pages=20
)
```

**参数:**
- `category_url` (str): 分类页面URL
- `max_pages` (int): 最大爬取页面数

**返回:**
- `list`: 包含所有爬取内容的列表

### AdvancedScraper

高级多线程爬虫类，支持并发爬取。

#### 初始化

```python
from src.scrapers import AdvancedScraper

scraper = AdvancedScraper(
    max_workers=5,
    delay_range=(1, 3),
    config_file="config/scraper_config.yaml"
)
```

#### 参数

- `max_workers` (int): 最大并发线程数
- `delay_range` (tuple): 请求延迟范围
- `config_file` (str): 配置文件路径

#### 方法

##### start_scraping(urls)

开始并发爬取多个URL。

```python
urls = ["https://example.com/page1", "https://example.com/page2"]
results = scraper.start_scraping(urls)
```

## 数据处理模块 (src.data_processing)

### DataCleaner

数据清洗工具类。

#### 初始化

```python
from src.data_processing import DataCleaner

cleaner = DataCleaner(
    input_dir="raw_data",
    output_dir="cleaned_data"
)
```

#### 方法

##### clean_text(text)

清洗单个文本内容。

```python
cleaned = cleaner.clean_text("原始文本内容...")
```

##### clean_dataset()

清洗整个数据集。

```python
cleaner.clean_dataset()
```

### DatasetDeduplicator

数据去重工具类。

#### 初始化

```python
from src.data_processing import DatasetDeduplicator

deduplicator = DatasetDeduplicator(
    dataset_dir="dataset",
    similarity_threshold=0.9
)
```

#### 方法

##### deduplicate()

执行去重操作。

```python
stats = deduplicator.deduplicate()
```

**返回:**
- `dict`: 去重统计信息

## 分析模块 (src.analysis)

### DatasetAnalyzer

数据集统计分析工具。

#### 初始化

```python
from src.analysis import DatasetAnalyzer

analyzer = DatasetAnalyzer("dataset")
```

#### 方法

##### analyze()

执行完整的数据集分析。

```python
stats = analyzer.analyze()
```

##### generate_report()

生成分析报告。

```python
analyzer.generate_report(output_file="report.html")
```

### ContentClassifier

内容分类器。

#### 初始化

```python
from src.analysis import ContentClassifier

classifier = ContentClassifier(
    config_file="config/classifier_config.yaml"
)
```

#### 方法

##### train(dataset_path)

训练分类模型。

```python
metrics = classifier.train("dataset")
```

##### predict(text)

预测文本分类。

```python
result = classifier.predict("要分类的文本内容")
```

##### evaluate(test_data)

评估模型性能。

```python
metrics = classifier.evaluate(test_data)
```

## 工具模块 (src.utils)

### DatasetGenerator

数据集生成器。

#### 初始化

```python
from src.utils import DatasetGenerator

generator = DatasetGenerator()
```

#### 方法

##### generate_samples(count, category)

生成指定数量的样本。

```python
samples = generator.generate_samples(100, "都市激情")
```

##### create_balanced_dataset()

创建平衡的数据集。

```python
generator.create_balanced_dataset()
```

## 错误处理

所有API都会抛出相应的异常，建议使用try-catch进行错误处理：

```python
try:
    result = scraper.scrape_page(url)
except ConnectionError as e:
    print(f"网络连接错误: {e}")
except ValueError as e:
    print(f"参数错误: {e}")
except Exception as e:
    print(f"未知错误: {e}")
```

## 配置文件

### 爬虫配置 (config/scraper_config.yaml)

详细配置选项请参考配置文件中的注释。

### 分类器配置 (config/classifier_config.yaml)

包含模型参数、预处理选项等配置。

## 示例代码

### 完整的数据处理流程

```python
from src.scrapers import AdvancedScraper
from src.data_processing import DataCleaner, DatasetDeduplicator
from src.analysis import DatasetAnalyzer, ContentClassifier

# 1. 爬取数据
scraper = AdvancedScraper(max_workers=5)
urls = ["https://example.com/category1", "https://example.com/category2"]
scraper.start_scraping(urls)

# 2. 清洗数据
cleaner = DataCleaner("raw_data", "cleaned_data")
cleaner.clean_dataset()

# 3. 去重
deduplicator = DatasetDeduplicator("cleaned_data")
dedup_stats = deduplicator.deduplicate()

# 4. 分析
analyzer = DatasetAnalyzer("cleaned_data")
analysis_stats = analyzer.analyze()

# 5. 训练分类器
classifier = ContentClassifier()
metrics = classifier.train("cleaned_data")

print(f"去重统计: {dedup_stats}")
print(f"分析统计: {analysis_stats}")
print(f"分类器性能: {metrics}")
```

## 注意事项

1. 所有路径参数都支持相对路径和绝对路径
2. 配置文件使用YAML格式，注意缩进
3. 大量数据处理时建议监控内存使用情况
4. 爬虫使用时请遵守网站的robots.txt规则
5. 分类器训练需要足够的样本数据才能获得好的效果
